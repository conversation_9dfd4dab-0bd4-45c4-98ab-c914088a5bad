/**
 * Firebase Functions for User Data Management (2nd Generation)
 *
 * 사용자 계정 삭제 시 모든 관련 데이터를 안전하게 정리하는 Functions
 */

const { onCall, onRequest } = require('firebase-functions/v2/https');
const { onSchedule } = require('firebase-functions/v2/scheduler');
const admin = require("firebase-admin");
const axios = require('axios');
const crypto = require('crypto');

// Firebase Admin SDK 초기화 (이미 초기화되어 있으면 중복 방지)
if (!admin.apps.length) {
  const serviceAccount = require("./parabara-1a504-288e9c6e5d05.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: "parabara-1a504.firebasestorage.app"
  });
}

// 🔥 나이스페이 실제 운영 환경 설정
const NICEPAY_CONFIG = {
  BASE_URL: 'https://api.nicepay.co.kr',
  CLIENT_KEY: 'R2_88ae8fc2a6474a44ad2d3dbb8f331a46',
  SECRET_KEY: '0a6e0a5737e14f25be3e529f609ca40b',
};



// 🔥 reCAPTCHA 설정
const RECAPTCHA_CONFIG = {
  SECRET_KEY: '6LdfCK4rAAAAAHd8F9G2Ay7ceJLASCnluS7wJ1S0',
  VERIFY_URL: 'https://www.google.com/recaptcha/api/siteverify',
};

// 나이스페이 유틸리티 함수들
function generateBasicAuth(clientKey, secretKey) {
  const credentials = Buffer.from(`${clientKey}:${secretKey}`).toString('base64');
  return `Basic ${credentials}`;
}

function generateSignData(data) {
  return crypto.createHash('sha256').update(data).digest('hex');
}

function encryptAES(plainText, key) {
  const cipher = crypto.createCipher('aes-128-ecb', key.substring(0, 16));
  let encrypted = cipher.update(plainText, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted.toUpperCase();
}



// reCAPTCHA 검증 함수
async function verifyRecaptcha(token) {
  try {
    const response = await axios.post(RECAPTCHA_CONFIG.VERIFY_URL, null, {
      params: {
        secret: RECAPTCHA_CONFIG.SECRET_KEY,
        response: token
      },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    console.log('🔐 reCAPTCHA 검증 응답:', response.data);

    if (response.data.success) {
      return { success: true, score: response.data.score };
    } else {
      return {
        success: false,
        errors: response.data['error-codes'] || ['unknown-error']
      };
    }
  } catch (error) {
    console.error('❌ reCAPTCHA 검증 오류:', error);
    return { success: false, errors: ['verification-failed'] };
  }
}

/**
 * 사용자 데이터 완전 삭제 함수 (2세대 onCall)
 * 클라이언트에서 호출하여 모든 데이터를 안전하게 삭제합니다.
 */
exports.deleteUserData = onCall(async (request) => {
  // 인증 확인
  if (!request.auth) {
    throw new Error('인증이 필요합니다.');
  }

  const uid = request.auth.uid;
  const userEmail = request.auth.token.email || 'unknown';

  console.log(`🗑️ 사용자 데이터 삭제 시작: ${userEmail} (${uid})`);

  try {
    // 1. 사용자 전화번호 정보 미리 조회 (SMS 인증 데이터 삭제용)
    console.log(`📞 사용자 전화번호 정보 조회: ${uid}`);
    let userPhoneNumber = null;
    try {
      const userDoc = await admin.firestore().collection('users').doc(uid).get();
      if (userDoc.exists && userDoc.data().phone) {
        userPhoneNumber = userDoc.data().phone;
        console.log(`📞 사용자 전화번호 발견: ${userPhoneNumber}`);
      }
    } catch (e) {
      console.log(`📞 사용자 전화번호 조회 실패 (계속 진행): ${e.message}`);
    }

    // 2. SMS 인증 데이터 삭제 (전화번호 기반)
    if (userPhoneNumber) {
      console.log(`📱 SMS 인증 데이터 삭제 시작: ${userPhoneNumber}`);
      try {
        // sms_verifications 컬렉션에서 해당 전화번호 문서 삭제
        await admin.firestore().collection('sms_verifications').doc(userPhoneNumber).delete();
        console.log(`✅ SMS 인증 데이터 삭제 완료: ${userPhoneNumber}`);
      } catch (e) {
        console.log(`⚠️ SMS 인증 데이터 삭제 실패 (계속 진행): ${e.message}`);
      }

      // phone_numbers 컬렉션에서도 삭제
      try {
        await admin.firestore().collection('phone_numbers').doc(userPhoneNumber).delete();
        console.log(`✅ 전화번호 소유권 데이터 삭제 완료: ${userPhoneNumber}`);
      } catch (e) {
        console.log(`⚠️ 전화번호 소유권 데이터 삭제 실패 (계속 진행): ${e.message}`);
      }
    }

    // 3. UID 기반 SMS 인증 데이터 추가 삭제 (전화번호를 못 찾은 경우 대비)
    console.log(`📱 UID 기반 SMS 인증 데이터 검색 및 삭제: ${uid}`);
    try {
      const smsQuery = await admin.firestore()
        .collection('sms_verifications')
        .where('uid', '==', uid)
        .get();

      if (!smsQuery.empty) {
        const batch = admin.firestore().batch();
        smsQuery.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`✅ UID 기반 SMS 인증 데이터 삭제 완료: ${smsQuery.size}개 문서`);
      } else {
        console.log(`📱 UID 기반 SMS 인증 데이터 없음`);
      }
    } catch (e) {
      console.log(`⚠️ UID 기반 SMS 인증 데이터 삭제 실패 (계속 진행): ${e.message}`);
    }

    // 4. Firestore 데이터 재귀 삭제 (사용자 문서 + 모든 하위 컬렉션)
    console.log(`📄 Firestore 재귀 삭제 시작: ${uid}`);
    const userRef = admin.firestore().collection('users').doc(uid);
    await admin.firestore().recursiveDelete(userRef);
    console.log(`✅ Firestore 모든 데이터 삭제 완료: ${uid}`);

    // 5. Storage 파일 전체 삭제 (사용자 폴더 통째로)
    console.log(`🗂️ Storage 폴더 삭제 시작: users/${uid}/`);
    const bucket = admin.storage().bucket();
    const [files] = await bucket.getFiles({ prefix: `users/${uid}/` });

    if (files.length > 0) {
      await Promise.all(files.map(file => file.delete()));
      console.log(`✅ Storage 파일 삭제 완료: ${files.length}개 파일`);
    } else {
      console.log(`📁 Storage에 삭제할 파일 없음`);
    }

    // 6. Authentication 계정 삭제 (마지막에)
    console.log(`🔐 Auth 계정 삭제 시작: ${uid}`);
    await admin.auth().deleteUser(uid);
    console.log(`✅ Auth 계정 삭제 완료: ${uid}`);

    console.log(`🎉 사용자 데이터 완전 삭제 완료: ${userEmail} (${uid})`);

    return {
      success: true,
      message: '모든 사용자 데이터가 성공적으로 삭제되었습니다.',
      deletedUid: uid
    };

  } catch (error) {
    console.error(`❌ 사용자 데이터 삭제 실패: ${userEmail} (${uid})`, error);
    throw new Error(`데이터 삭제 중 오류가 발생했습니다: ${error.message}`);
  }
});

// ❌ createBillingKey 함수 제거됨 - 클라이언트에서 직접 처리

// ❌ processBillingPayment 함수 제거됨 - 클라이언트에서 직접 처리

/**
 * 🔄 정기 결제 시스템 (개별 구독일 기준)
 * Cloud Scheduler에 의해 매일 실행되는 함수입니다.
 * 오늘 결제해야 할 구독자들의 자동 결제를 처리합니다.
 */
exports.processDailySubscriptions = onRequest({
  cors: true,
  // 🔥 함수를 공개적으로 접근 가능하도록 설정
  invoker: 'public'
}, async (req, res) => {
  console.log('🔄 일일 정기 결제 시스템 시작');
  console.log('📝 요청 정보:', {
    method: req.method,
    headers: req.headers,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  });

  try {
    const db = admin.firestore();
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    console.log(`📅 오늘 날짜: ${today.toISOString().split('T')[0]}`);
    console.log(`📅 내일 날짜: ${tomorrow.toISOString().split('T')[0]}`);

    // 🔥 최적화: 결제 스케줄 컬렉션 사용 (collectionGroup 대신)
    console.log('🔍 오늘 결제 스케줄 조회 시작...');

    const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD
    const paymentScheduleDoc = await db.collection('payment_schedule').doc(todayStr).get();

    let todaySubscriptions = [];

    if (paymentScheduleDoc.exists) {
      const scheduleData = paymentScheduleDoc.data();
      const userIds = scheduleData.userIds || [];

      console.log(`📋 오늘 결제 예정 사용자: ${userIds.length}명`);

      // 각 사용자의 구독 정보 조회 (효율적)
      for (const userId of userIds) {
        try {
          const subscriptionDoc = await db.collection('users')
            .doc(userId)
            .collection('subscriptions')
            .doc('current')
            .get();

          if (subscriptionDoc.exists) {
            const subscription = subscriptionDoc.data();

            // 여전히 활성 상태이고 오늘 결제해야 하는지 확인
            if (subscription.status === 'active') {
              const nextPaymentDate = subscription.nextPaymentDate;
              const todayTimestamp = admin.firestore.Timestamp.fromDate(today);
              const tomorrowTimestamp = admin.firestore.Timestamp.fromDate(tomorrow);

              if (nextPaymentDate &&
                  nextPaymentDate >= todayTimestamp &&
                  nextPaymentDate < tomorrowTimestamp) {
                todaySubscriptions.push({
                  doc: subscriptionDoc,
                  data: subscription
                });
              }
            }
          }
        } catch (error) {
          console.error(`사용자 ${userId} 구독 조회 오류:`, error);
        }
      }
    } else {
      console.log('📋 오늘 결제 스케줄이 없습니다. 전체 스캔으로 대체...');

      // 스케줄이 없으면 기존 방식으로 대체 (안전장치)
      const subscriptionsSnapshot = await db.collectionGroup('subscriptions')
        .where('status', '==', 'active')
        .get();

      const todayTimestamp = admin.firestore.Timestamp.fromDate(today);
      const tomorrowTimestamp = admin.firestore.Timestamp.fromDate(tomorrow);

      subscriptionsSnapshot.forEach(doc => {
        const subscription = doc.data();
        const nextPaymentDate = subscription.nextPaymentDate;

        if (nextPaymentDate &&
            nextPaymentDate >= todayTimestamp &&
            nextPaymentDate < tomorrowTimestamp) {
          todaySubscriptions.push({
            doc: doc,
            data: subscription
          });
        }
      });
    }

    console.log(`📋 오늘 처리할 구독 수: ${todaySubscriptions.length}`);

    if (todaySubscriptions.length === 0) {
      console.log('✅ 오늘 처리할 구독이 없습니다.');

      // 자동 결제 시스템 로그 저장 (0건 처리)
      try {
        const now = new Date();
        // 한국 시간(KST, UTC+9)으로 변환
        const kstTime = new Date(now.getTime() + (9 * 60 * 60 * 1000));
        const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
        const docName = `자동결제로그 : ${logTime} KST`;

        await db.collection('auto_payment_logs').doc(docName).set({
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          type: 'daily_subscription_check',
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          message: '처리할 구독이 없습니다',
          level: 'INFO',
          details: null,
          executedBy: 'Cloud Scheduler'
        });
        console.log('✅ 자동 결제 로그 저장 완료 (0건)');
      } catch (logError) {
        console.error('❌ 자동 결제 로그 저장 실패:', logError);
      }

      return res.status(200).json({
        success: true,
        message: '오늘 처리할 구독이 없습니다.',
        processedCount: 0,
        results: []
      });
    }

    const results = [];

    for (const subscriptionItem of todaySubscriptions) {
      const subscriptionDoc = subscriptionItem.doc;
      const subscription = subscriptionItem.data;
      const userId = subscriptionDoc.ref.parent.parent.id;

      console.log(`💳 사용자 ${userId} 정기 결제 처리 시작`);

      try {
        console.log(`🔍 사용자 ${userId} 구독 정보:`, {
          status: subscription.status,
          nextPaymentDate: subscription.nextPaymentDate?.toDate?.()?.toISOString(),
          plan: subscription.plan
        });

        // 사용자의 활성 카드 정보 가져오기
        console.log(`💳 사용자 ${userId} 활성 카드 조회 중...`);
        const cardSnapshot = await db.collection('users')
          .doc(userId)
          .collection('cards')
          .where('isActive', '==', true)
          .limit(1)
          .get();

        if (cardSnapshot.empty) {
          console.error(`❌ 사용자 ${userId}: 활성 카드 없음`);
          results.push({ userId, success: false, error: '활성 카드 없음' });
          continue;
        }

        const cardData = cardSnapshot.docs[0].data();
        const bid = cardData.bid;
        console.log(`💳 사용자 ${userId} 활성 카드 발견: ${bid}`);

        // 정기 결제 실행
        console.log(`💰 사용자 ${userId} 정기 결제 실행 중...`);
        const paymentResult = await processSubscriptionPayment(bid, userId, subscription);

        if (paymentResult.success) {
          // 🔥 다음 결제일 계산 (개별 구독일 기준)
          const currentPaymentDate = subscription.nextPaymentDate.toDate();
          const nextPaymentDate = calculateNextPaymentDate(currentPaymentDate);

          console.log(`📅 사용자 ${userId} 다음 결제일 업데이트: ${nextPaymentDate.toISOString()}`);

          await subscriptionDoc.ref.update({
            lastPaymentDate: admin.firestore.Timestamp.fromDate(now),
            nextPaymentDate: admin.firestore.Timestamp.fromDate(nextPaymentDate),
            lastPaymentData: paymentResult.data,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });

          console.log(`✅ 사용자 ${userId} 정기 결제 성공 - TID: ${paymentResult.data?.tid}`);
          results.push({ userId, success: true, tid: paymentResult.data?.tid });
        } else {
          console.error(`❌ 사용자 ${userId} 정기 결제 실패:`, paymentResult.error);
          results.push({ userId, success: false, error: paymentResult.error });
        }

      } catch (error) {
        console.error(`❌ 사용자 ${userId} 처리 중 오류:`, {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        results.push({ userId, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    console.log('🎉 정기 결제 시스템 완료:', {
      총처리수: results.length,
      성공: successCount,
      실패: failureCount,
      결과: results
    });

    // 자동 결제 시스템 로그 저장
    try {
      const now = new Date();
      // 한국 시간(KST, UTC+9)으로 변환
      const kstTime = new Date(now.getTime() + (9 * 60 * 60 * 1000));
      const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
      const docName = `자동결제로그 : ${logTime} KST`;

      await db.collection('auto_payment_logs').doc(docName).set({
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        type: 'daily_subscription_check',
        totalProcessed: results.length,
        successCount: successCount,
        failureCount: failureCount,
        message: results.length === 0 ? '처리할 구독이 없습니다' : `정기 결제 처리 완료 - 성공: ${successCount}, 실패: ${failureCount}`,
        level: failureCount > 0 ? 'WARNING' : 'SUCCESS',
        details: results.length > 0 ? JSON.stringify(results) : null,
        executedBy: 'Cloud Scheduler'
      });
      console.log('✅ 자동 결제 로그 저장 완료');
    } catch (logError) {
      console.error('❌ 자동 결제 로그 저장 실패:', logError);
    }

    // 🔥 성공적으로 응답 반환
    res.status(200).json({
      success: true,
      message: `정기 결제 처리 완료 - 성공: ${successCount}, 실패: ${failureCount}`,
      processedCount: results.length,
      successCount: successCount,
      failureCount: failureCount,
      results: results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 정기 결제 시스템 전체 오류:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    // 자동 결제 시스템 오류 로그 저장
    try {
      const now = new Date();
      // 한국 시간(KST, UTC+9)으로 변환
      const kstTime = new Date(now.getTime() + (9 * 60 * 60 * 1000));
      const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
      const docName = `자동결제로그 : ${logTime} KST`;

      await admin.firestore().collection('auto_payment_logs').doc(docName).set({
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        type: 'daily_subscription_check',
        totalProcessed: 0,
        successCount: 0,
        failureCount: 0,
        message: `시스템 오류: ${error.message}`,
        level: 'ERROR',
        details: JSON.stringify({
          error: error.message,
          stack: error.stack,
          name: error.name
        }),
        executedBy: 'Cloud Scheduler'
      });
      console.log('✅ 자동 결제 오류 로그 저장 완료');
    } catch (logError) {
      console.error('❌ 자동 결제 오류 로그 저장 실패:', logError);
    }

    // 🔥 오류 응답도 성공적으로 반환 (Cloud Scheduler가 재시도하지 않도록)
    res.status(200).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 개별 구독 결제 처리
 */
async function processSubscriptionPayment(bid, userId, subscription) {
  try {
    // orderId 생성
    const orderId = `MONTHLY_${Date.now()}_${userId.substring(0, 8)}`;

    // ediDate 생성 (YYYYMMDDHHMMSS 형식)
    const now = new Date();
    const ediDate = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;

    // signData 생성
    const signDataInput = `${orderId}${bid}${ediDate}${NICEPAY_CONFIG.SECRET_KEY}`;
    const signData = crypto.createHash('sha256').update(signDataInput).digest('hex');

    // 구매자 정보 가져오기
    const cardDoc = await admin.firestore()
      .collection('users')
      .doc(userId)
      .collection('cards')
      .doc(bid)
      .get();

    const cardData = cardDoc.data();

    // 🔥 플랜별 금액 및 상품명 설정
    let amount, goodsName;
    const planType = subscription.planType || subscription.plan; // planType 또는 plan 필드 사용

    if (planType === 'plus' || planType === 'SubscriptionPlanType.plus') {
      amount = 3500; // 플러스 플랜: 3,500원
      goodsName = '바라 부스 매니저 플러스 플랜';
    } else if (planType === 'pro' || planType === 'SubscriptionPlanType.pro') {
      amount = 4900; // 프로 플랜: 4,900원
      goodsName = '바라 부스 매니저 프로 플랜';
    } else {
      // 기본값 (하위 호환성)
      amount = subscription.price || 4900;
      goodsName = '바라 부스 매니저 구독';
    }

    console.log(`💰 사용자 ${userId} 결제 정보: 플랜=${planType}, 금액=${amount}원`);

    // 나이스페이 결제 API 호출
    const response = await axios.post(
      `${NICEPAY_CONFIG.BASE_URL}/v1/subscribe/${bid}/payments`,
      {
        orderId: orderId,
        amount: amount,
        goodsName: goodsName,
        cardQuota: '0',
        useShopInterest: false,
        buyerName: cardData?.buyerName || '구매자',
        buyerEmail: cardData?.buyerEmail || '<EMAIL>',
        buyerTel: cardData?.buyerTel || '01012345678',
        ediDate: ediDate,
        signData: signData,
        returnCharSet: 'utf-8',
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': generateBasicAuth(NICEPAY_CONFIG.CLIENT_KEY, NICEPAY_CONFIG.SECRET_KEY),
        },
      }
    );

    if (response.data.resultCode === '0000') {
      // 결제 내역 저장
      await admin.firestore()
        .collection('users')
        .doc(userId)
        .collection('payments')
        .doc(response.data.tid)
        .set({
          tid: response.data.tid,
          orderId: orderId,
          amount: amount, // 실제 결제 금액 사용
          status: response.data.status,
          description: `정기 구독 결제 (${goodsName})`,
          planType: planType, // 플랜 타입 추가
          paymentData: response.data,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      return { success: true, data: response.data };
    } else {
      return { success: false, error: response.data.resultMsg };
    }

  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 다음 결제일 계산 (개별 구독일 기준)
 * 29, 30, 31일 구독자 처리 포함
 */
function calculateNextPaymentDate(currentPaymentDate) {
  const nextMonth = new Date(currentPaymentDate);
  nextMonth.setMonth(nextMonth.getMonth() + 1);

  // 원래 구독일
  const originalDay = currentPaymentDate.getDate();

  // 다음 달의 마지막 날
  const lastDayOfNextMonth = new Date(nextMonth.getFullYear(), nextMonth.getMonth() + 1, 0).getDate();

  // 🔥 29, 30, 31일 구독자 처리
  if (originalDay > lastDayOfNextMonth) {
    // 다음 달에 해당 날짜가 없으면 마지막 날로 설정
    nextMonth.setDate(lastDayOfNextMonth);
  } else {
    nextMonth.setDate(originalDay);
  }

  console.log(`📅 다음 결제일 계산: ${currentPaymentDate.toISOString().split('T')[0]} → ${nextMonth.toISOString().split('T')[0]}`);

  return nextMonth;
}

/**
 * 🔐 관리자 인증 함수
 */
exports.adminAuth = onRequest({ cors: true }, async (req, res) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { username, password } = req.body;

  // 🔐 관리자 계정 확인 (환경변수 사용)
  const ADMIN_USERNAME = process.env.ADMIN_USERNAME;
  const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;

  if (!ADMIN_USERNAME || !ADMIN_PASSWORD) {
    return res.status(500).json({
      success: false,
      message: '관리자 계정 설정 오류'
    });
  }

  if (username === ADMIN_USERNAME && password === ADMIN_PASSWORD) {
    // JWT 토큰 생성
    const jwt = require('jsonwebtoken');

    // JWT 시크릿 키
    const JWT_SECRET = process.env.JWT_SECRET || 'parabara-admin-secret-key-2024';

    // JWT 페이로드
    const payload = {
      admin: true,
      username: username,
      loginTime: Date.now(),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24시간 만료
    };

    const token = jwt.sign(payload, JWT_SECRET);

    // 접근 로그 기록
    const loginLog = {
      timestamp: new Date().toISOString(),
      username: username,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      success: true
    };

    // 비동기로 로그 저장
    setImmediate(async () => {
      try {
        const now = new Date();
        const logTime = now.toISOString().replace('T', ' ').substring(0, 19);
        const docName = `관리자로그 : ${logTime}`;

        await admin.firestore().collection('admin_logs').doc(docName).set(loginLog);
      } catch (error) {
        console.error('관리자 로그 저장 오류:', error);
      }
    });

    console.log('✅ 관리자 로그인 성공:', loginLog);

    res.json({
      success: true,
      token: token,
      message: '관리자 인증 성공',
      expiresIn: '24시간'
    });
  } else {
    // 실패 로그 기록
    const failLog = {
      timestamp: new Date().toISOString(),
      username: username,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      success: false,
      reason: '잘못된 인증정보'
    };

    setImmediate(async () => {
      try {
        const now = new Date();
        const logTime = now.toISOString().replace('T', ' ').substring(0, 19);
        const docName = `관리자로그 : ${logTime}`;

        await admin.firestore().collection('admin_logs').doc(docName).set(failLog);
      } catch (error) {
        console.error('관리자 로그 저장 오류:', error);
      }
    });

    console.log('❌ 관리자 로그인 실패:', failLog);

    res.status(401).json({
      success: false,
      message: '인증 실패'
    });
  }
});

/**
 * 📊 구독 통계 전용 컬렉션 초기화
 * subscription_stats 컬렉션을 생성하고 초기 데이터를 설정합니다.
 */
async function initializeSubscriptionStats(db) {
  try {
    console.log('📊 subscription_stats 컬렉션 초기화 시작');

    const now = new Date();

    // 활성 구독자 수 계산
    const activeSubscriptions = await db.collectionGroup('subscriptions')
      .where('status', '==', 'active')
      .get();
    const activeSubscribers = activeSubscriptions.size;

    // 월별 매출 통계 계산 (최근 6개월)
    const monthlyRevenue = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

      const monthlySubscriptions = await db.collectionGroup('subscriptions')
        .where('status', '==', 'active')
        .where('lastPaymentDate', '>=', admin.firestore.Timestamp.fromDate(monthStart))
        .where('lastPaymentDate', '<=', admin.firestore.Timestamp.fromDate(monthEnd))
        .get();

      monthlyRevenue.push({
        month: monthStart.toISOString().substring(0, 7), // YYYY-MM 형식
        revenue: monthlySubscriptions.size * 100, // 월 100원 * 구독자 수
        subscribers: monthlySubscriptions.size
      });
    }

    // subscription_stats 컬렉션에 저장
    await db.collection('subscription_stats').doc('current').set({
      activeSubscribers,
      monthlyRevenue,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
      initializedAt: now.toISOString()
    });

    console.log(`📊 subscription_stats 초기화 완료 - 활성 구독자: ${activeSubscribers}명`);

  } catch (error) {
    console.error('subscription_stats 초기화 오류:', error);
  }
}

/**
 * 📊 구독 통계 업데이트
 * 구독 상태 변경 시 subscription_stats를 업데이트합니다.
 */
exports.updateSubscriptionStats = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  try {
    const db = admin.firestore();
    await initializeSubscriptionStats(db);

    res.json({
      success: true,
      message: 'subscription_stats 업데이트 완료'
    });

  } catch (error) {
    console.error('subscription_stats 업데이트 오류:', error);
    res.status(500).json({
      success: false,
      error: 'subscription_stats 업데이트 실패'
    });
  }
});

/**
 * 📅 결제 스케줄 생성 (매일 실행)
 * 다음날 결제해야 할 사용자 목록을 미리 생성하여 성능 최적화
 */
exports.generatePaymentSchedule = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  console.log('📅 결제 스케줄 생성 시작');

  try {
    const db = admin.firestore();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStart = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate());
    const tomorrowEnd = new Date(tomorrowStart);
    tomorrowEnd.setDate(tomorrowEnd.getDate() + 1);

    const tomorrowStr = tomorrowStart.toISOString().split('T')[0];

    console.log(`📅 내일 날짜: ${tomorrowStr}`);

    // 내일 결제해야 할 구독들 조회 (한 번만 실행)
    const subscriptionsSnapshot = await db.collectionGroup('subscriptions')
      .where('status', '==', 'active')
      .where('nextPaymentDate', '>=', admin.firestore.Timestamp.fromDate(tomorrowStart))
      .where('nextPaymentDate', '<', admin.firestore.Timestamp.fromDate(tomorrowEnd))
      .get();

    const userIds = [];
    subscriptionsSnapshot.forEach(doc => {
      const userId = doc.ref.parent.parent.id;
      if (!userIds.includes(userId)) {
        userIds.push(userId);
      }
    });

    // 결제 스케줄 저장
    await db.collection('payment_schedule').doc(tomorrowStr).set({
      date: tomorrowStr,
      userIds: userIds,
      totalUsers: userIds.length,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log(`✅ 내일(${tomorrowStr}) 결제 스케줄 생성 완료: ${userIds.length}명`);

    // 결제 스케줄 생성 로그 저장
    try {
      const now = new Date();
      const logTime = now.toISOString().replace('T', ' ').substring(0, 19);
      const docName = `자동결제로그 : ${logTime}`;

      await db.collection('auto_payment_logs').doc(docName).set({
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        type: 'payment_schedule_generation',
        totalProcessed: userIds.length,
        successCount: userIds.length,
        failureCount: 0,
        message: `내일(${tomorrowStr}) 결제 스케줄 생성 완료: ${userIds.length}명`,
        level: 'SUCCESS',
        details: JSON.stringify({ date: tomorrowStr, userIds: userIds }),
        executedBy: 'Cloud Scheduler'
      });
      console.log('✅ 결제 스케줄 생성 로그 저장 완료');
    } catch (logError) {
      console.error('❌ 결제 스케줄 생성 로그 저장 실패:', logError);
    }

    res.status(200).json({
      success: true,
      date: tomorrowStr,
      totalUsers: userIds.length,
      message: '결제 스케줄 생성 완료'
    });

  } catch (error) {
    console.error('❌ 결제 스케줄 생성 오류:', error);

    // 결제 스케줄 생성 오류 로그 저장
    try {
      const now = new Date();
      const logTime = now.toISOString().replace('T', ' ').substring(0, 19);
      const docName = `자동결제로그 : ${logTime}`;

      await admin.firestore().collection('auto_payment_logs').doc(docName).set({
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        type: 'payment_schedule_generation',
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        message: `결제 스케줄 생성 오류: ${error.message}`,
        level: 'ERROR',
        details: JSON.stringify({
          error: error.message,
          stack: error.stack,
          name: error.name
        }),
        executedBy: 'Cloud Scheduler'
      });
      console.log('✅ 결제 스케줄 생성 오류 로그 저장 완료');
    } catch (logError) {
      console.error('❌ 결제 스케줄 생성 오류 로그 저장 실패:', logError);
    }

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 🔐 JWT 토큰 검증 미들웨어
 */
function verifyAdminToken(req, res, next) {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: '인증 토큰이 필요합니다.'
    });
  }

  const token = authHeader.split('Bearer ')[1];
  const jwt = require('jsonwebtoken');
  const JWT_SECRET = process.env.JWT_SECRET || 'parabara-admin-secret-key-2024';

  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    if (!decoded.admin) {
      return res.status(403).json({
        success: false,
        error: '관리자 권한이 필요합니다.'
      });
    }

    req.admin = decoded;
    next();
  } catch (error) {
    console.error('JWT 토큰 검증 실패:', error.message);
    return res.status(401).json({
      success: false,
      error: '유효하지 않은 토큰입니다.'
    });
  }
}

/**
 * 📊 관리자 대시보드 데이터 조회 (온디맨드 + 스마트 캐싱)
 * 관리자가 대시보드에 접속할 때만 통계를 생성하고, 캐시를 활용하여 성능 최적화
 */
exports.adminDashboard = onRequest({ cors: true }, async (req, res) => {
  // 간단한 토큰 검증 (실제 운영에서는 더 강화된 검증 필요)
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: '인증 토큰이 필요합니다' });
  }

  try {
    const db = admin.firestore();
    const now = new Date();

    // 캐시 확인 (6시간 캐시)
    const cacheKey = 'admin_dashboard_stats';
    const cacheExpiry = 6 * 60 * 60 * 1000; // 6시간

    try {
      const cachedDoc = await db.collection('admin_cache').doc(cacheKey).get();
      if (cachedDoc.exists) {
        const cachedData = cachedDoc.data();
        const cacheAge = now.getTime() - cachedData.timestamp.toDate().getTime();

        if (cacheAge < cacheExpiry) {
          console.log(`📊 캐시된 대시보드 데이터 반환 (${Math.floor(cacheAge / 60000)}분 전 생성)`);
          return res.json({
            success: true,
            data: cachedData.stats,
            cached: true,
            cacheAge: Math.floor(cacheAge / 60000) + '분 전'
          });
        }
      }
    } catch (cacheError) {
      console.log('캐시 조회 실패, 실시간 생성으로 진행:', cacheError.message);
    }

    console.log('📊 실시간 대시보드 통계 생성 시작');
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // 전체 사용자 수
    const usersSnapshot = await db.collection('users').get();
    const totalUsers = usersSnapshot.size;

    // 최근 30일 신규 가입자 수
    const recentUsersSnapshot = await db.collection('users')
      .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(thirtyDaysAgo))
      .get();
    const recentUsers = recentUsersSnapshot.size;

    // 최근 7일 신규 가입자 수
    const weeklyUsersSnapshot = await db.collection('users')
      .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(sevenDaysAgo))
      .get();
    const weeklyUsers = weeklyUsersSnapshot.size;

    // 활성 구독자 수 (subscription_stats 컬렉션 사용)
    let activeSubscribers = 0;
    let monthlyRevenue = [];
    try {
      const subscriptionStatsDoc = await db.collection('subscription_stats').doc('current').get();
      if (subscriptionStatsDoc.exists) {
        const statsData = subscriptionStatsDoc.data();
        activeSubscribers = statsData.activeSubscribers || 0;
        monthlyRevenue = statsData.monthlyRevenue || [];
        console.log('📊 구독 통계 전용 컬렉션에서 데이터 조회 완료');
      } else {
        // 백업: 실시간 조회 (subscription_stats가 없는 경우)
        console.log('⚠️ subscription_stats 없음, 실시간 조회로 대체');
        const activeSubscriptions = await db.collectionGroup('subscriptions')
          .where('status', '==', 'active')
          .get();
        activeSubscribers = activeSubscriptions.size;

        // subscription_stats 초기화
        await initializeSubscriptionStats(db);
      }
    } catch (error) {
      console.error('구독 통계 조회 오류:', error);
      activeSubscribers = 0;
      monthlyRevenue = [];
    }

    // 무료 플랜 사용자 수
    const freeUsers = totalUsers - activeSubscribers;

    // 최근 결제 내역 (더 자세한 정보 포함)
    const recentPayments = await db.collectionGroup('subscriptions')
      .where('status', '==', 'active')
      .orderBy('lastPaymentDate', 'desc')
      .limit(10)
      .get();

    const payments = [];
    for (const doc of recentPayments.docs) {
      const subscriptionData = doc.data();
      const userId = doc.ref.parent.parent.id;

      // 사용자 정보 가져오기
      const userDoc = await db.collection('users').doc(userId).get();
      const userData = userDoc.exists ? userDoc.data() : {};

      payments.push({
        id: doc.id,
        userId: userId,
        userEmail: userData.email || 'N/A',
        userNickname: userData.nickname || 'N/A',
        amount: 100, // 월 100원 고정
        plan: subscriptionData.plan || 'pro',
        status: subscriptionData.status,
        lastPaymentDate: subscriptionData.lastPaymentDate?.toDate?.()?.toISOString() || null,
        nextPaymentDate: subscriptionData.nextPaymentDate?.toDate?.()?.toISOString() || null,
        paymentMethod: 'card'
      });
    }

    // 월별 매출 통계는 subscription_stats에서 이미 가져옴 (위에서 처리됨)

    // 서버 사용량 통계 (추정치)
    const serverStats = {
      totalFunctionCalls: Math.floor(Math.random() * 10000) + 5000, // 실제로는 Cloud Monitoring에서 가져와야 함
      totalStorageUsed: Math.floor(Math.random() * 1000) + 500, // MB 단위
      totalBandwidthUsed: Math.floor(Math.random() * 5000) + 2000, // MB 단위
      averageResponseTime: Math.floor(Math.random() * 500) + 100, // ms 단위
      errorRate: (Math.random() * 2).toFixed(2) + '%'
    };

    // 사용자 활동 통계
    const userActivityStats = {
      dailyActiveUsers: Math.floor(totalUsers * 0.3), // 추정치
      weeklyActiveUsers: Math.floor(totalUsers * 0.6), // 추정치
      monthlyActiveUsers: Math.floor(totalUsers * 0.8), // 추정치
      averageSessionDuration: Math.floor(Math.random() * 30) + 15 + ' minutes'
    };

    res.json({
      success: true,
      data: {
        // 기본 통계
        totalUsers,
        activeSubscribers,
        freeUsers,
        recentUsers,
        weeklyUsers,

        // 매출 통계
        monthlyRevenue,
        totalRevenue: activeSubscribers * 100, // 현재 월 매출

        // 결제 내역
        recentPayments: payments,

        // 서버 사용량
        serverStats,

        // 사용자 활동
        userActivityStats,

        // 성장률 계산
        growthRate: {
          weekly: weeklyUsers > 0 ? ((weeklyUsers / Math.max(totalUsers - weeklyUsers, 1)) * 100).toFixed(1) + '%' : '0%',
          monthly: recentUsers > 0 ? ((recentUsers / Math.max(totalUsers - recentUsers, 1)) * 100).toFixed(1) + '%' : '0%'
        },

        timestamp: now.toISOString()
      }
    };

    // 캐시에 저장 (백그라운드에서 비동기 처리)
    const statsData = responseData.data;
    setImmediate(async () => {
      try {
        await db.collection('admin_cache').doc(cacheKey).set({
          stats: statsData,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          generatedAt: now.toISOString()
        });
        console.log('📊 대시보드 통계 캐시 저장 완료');
      } catch (cacheError) {
        console.error('캐시 저장 오류:', cacheError);
      }
    });

    res.json(responseData);

  } catch (error) {
    console.error('관리자 대시보드 오류:', error);
    res.status(500).json({
      success: false,
      error: '데이터 조회 중 오류가 발생했습니다'
    });
  }
});

/**
 * 👥 사용자 목록 조회 (고급 검색/정렬/필터링 지원)
 */
exports.adminUsers = onRequest({ cors: true }, async (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: '인증 토큰이 필요합니다' });
  }

  try {
    const db = admin.firestore();

    // 쿼리 파라미터 파싱
    const {
      page = 1,
      limit = 50,
      search = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      subscriptionFilter = 'all'
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    // 기본 사용자 쿼리
    let usersQuery = db.collection('users');

    // 정렬 적용
    if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
      usersQuery = usersQuery.orderBy(sortBy, sortOrder);
    } else if (sortBy === 'email') {
      usersQuery = usersQuery.orderBy('email', sortOrder);
    }

    // 페이지네이션 적용
    if (offset > 0) {
      const offsetSnapshot = await usersQuery.limit(offset).get();
      if (!offsetSnapshot.empty) {
        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        usersQuery = usersQuery.startAfter(lastDoc);
      }
    }

    const usersSnapshot = await usersQuery.limit(limitNum).get();

    // 🔥 최적화: 배치 처리로 N+1 문제 해결
    const users = [];
    const userIds = usersSnapshot.docs.map(doc => doc.id);

    // 모든 사용자의 구독 정보를 한 번에 조회
    const subscriptionPromises = userIds.map(userId =>
      db.collection('users').doc(userId).collection('subscriptions').doc('current').get()
    );
    const subscriptionDocs = await Promise.all(subscriptionPromises);

    // 모든 사용자의 설정 정보를 한 번에 조회
    const settingsPromises = userIds.map(userId =>
      db.collection('users').doc(userId).collection('settings').doc('user_settings').get()
    );
    const settingsDocs = await Promise.all(settingsPromises);

    // 결과 조합
    for (let i = 0; i < usersSnapshot.docs.length; i++) {
      const userDoc = usersSnapshot.docs[i];
      const userData = userDoc.data();
      const subscriptionDoc = subscriptionDocs[i];
      const settingsDoc = settingsDocs[i];

      const subscriptionData = subscriptionDoc.exists ? subscriptionDoc.data() : null;
      const settingsData = settingsDocs[i].exists ? settingsDocs[i].data() : null;

      // 사용자 정보 구성
      const userInfo = {
        uid: userDoc.id,
        email: userData.email || 'N/A',
        nickname: userData.nickname || 'N/A', // displayName 대신 nickname 사용
        phone: userData.phone || 'N/A',
        profileImageUrl: userData.profileImageUrl || null,
        createdAt: userData.createdAt?.toDate?.()?.toISOString() || null,
        updatedAt: userData.updatedAt?.toDate?.()?.toISOString() || null,
        lastLoginAt: userData.lastLoginAt?.toDate?.()?.toISOString() || null,

        // 구독 정보
        subscription: subscriptionData ? {
          status: subscriptionData.status,
          plan: subscriptionData.plan,
          nextPaymentDate: subscriptionData.status === 'active' ?
            subscriptionData.nextPaymentDate?.toDate?.()?.toISOString() : null,
          lastPaymentDate: subscriptionData.lastPaymentDate?.toDate?.()?.toISOString() || null,
          createdAt: subscriptionData.createdAt?.toDate?.()?.toISOString() || null
        } : {
          status: 'free',
          plan: 'free',
          nextPaymentDate: null,
          lastPaymentDate: null,
          createdAt: null
        },

        // 설정 정보
        settings: settingsData ? {
          realtimeSyncEnabled: settingsData.realtimeSyncEnabled || false,
          subscriptionPlanType: settingsData.subscriptionPlanType || 'free',
          deviceId: settingsData.deviceId || null,
          lastWorkspaceId: settingsData.lastWorkspaceId || null
        } : null,

        // 약관 동의 정보
        agreement: userData.agreement || null
      };

      // 검색 필터 적용
      if (search) {
        const searchLower = search.toLowerCase();
        const emailMatch = userInfo.email.toLowerCase().includes(searchLower);
        const nicknameMatch = userInfo.nickname.toLowerCase().includes(searchLower);
        const phoneMatch = userInfo.phone.toLowerCase().includes(searchLower);

        if (!emailMatch && !nicknameMatch && !phoneMatch) {
          continue;
        }
      }

      // 구독 상태 필터 적용
      if (subscriptionFilter !== 'all') {
        if (subscriptionFilter === 'active' && userInfo.subscription.status !== 'active') {
          continue;
        }
        if (subscriptionFilter === 'free' && userInfo.subscription.status !== 'free') {
          continue;
        }
      }

      users.push(userInfo);
    }

    // 전체 사용자 수 조회 (페이지네이션용)
    const totalUsersSnapshot = await db.collection('users').get();
    const totalUsers = totalUsersSnapshot.size;

    res.json({
      success: true,
      data: {
        users: users,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(totalUsers / limitNum),
          totalUsers: totalUsers,
          hasNextPage: pageNum * limitNum < totalUsers,
          hasPrevPage: pageNum > 1
        }
      }
    });

  } catch (error) {
    console.error('사용자 목록 조회 오류:', error);
    res.status(500).json({
      success: false,
      error: '사용자 목록 조회 중 오류가 발생했습니다'
    });
  }
});

/**
 * 나이스페이 웹훅 처리 함수
 * 나이스페이에서 결제 결과를 통보받는 엔드포인트입니다.
 */
exports.nicepayWebhook = onRequest(async (req, res) => {
  console.log('🔔 나이스페이 웹훅 수신:', {
    method: req.method,
    headers: req.headers,
    body: req.body,
    query: req.query
  });

  try {
    // CORS 헤더 설정
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type');

    // OPTIONS 요청 처리 (CORS preflight)
    if (req.method === 'OPTIONS') {
      return res.status(200).send();
    }

    // GET 요청 처리 (나이스페이 URL 검증)
    if (req.method === 'GET') {
      console.log('✅ 나이스페이 웹훅 URL 검증 요청');
      return res.status(200).send('Nicepay Webhook URL is valid');
    }

    // POST 요청 처리 (실제 웹훅 데이터)
    if (req.method === 'POST') {
      const webhookData = req.body || {};
      console.log('📦 웹훅 데이터:', webhookData);

      // 기본 응답 (나이스페이에서 요구하는 형식)
      return res.status(200).send('OK');
    }

    // 기타 요청
    return res.status(405).send('Method Not Allowed');

  } catch (error) {
    console.error('❌ 웹훅 처리 오류:', error);
    return res.status(200).send('OK'); // 나이스페이에는 항상 200 응답
  }
});

/**
 * 🔧 관리자 구독 상태 변경
 * 관리자가 사용자의 구독 상태를 변경할 수 있는 함수입니다.
 */
exports.adminToggleSubscription = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  console.log('🔧 관리자 구독 상태 변경 요청');

  try {
    // 관리자 인증 확인
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '인증이 필요합니다.'
      });
    }

    const token = authHeader.split('Bearer ')[1];
    // TODO: 실제 토큰 검증 로직 추가

    const { uid, action } = req.body;
    if (!uid || !action) {
      return res.status(400).json({
        success: false,
        error: 'uid와 action이 필요합니다.'
      });
    }

    const db = admin.firestore();
    const userRef = db.collection('users').doc(uid);
    const subscriptionRef = userRef.collection('subscriptions').doc('current');

    if (action === 'activate') {
      // 구독 활성화
      await subscriptionRef.set({
        status: 'active',
        plan: 'pro',
        price: 100,
        startedAt: admin.firestore.FieldValue.serverTimestamp(),
        subscriptionDay: new Date().getDate(),
        nextPaymentDate: admin.firestore.Timestamp.fromDate(
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30일 후
        ),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        adminActivated: true
      }, { merge: true });

      console.log(`✅ 사용자 ${uid} 구독 활성화 완료`);
    } else if (action === 'deactivate') {
      // 구독 비활성화
      await subscriptionRef.update({
        status: 'free',
        plan: 'free',
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        adminDeactivated: true
      });

      console.log(`✅ 사용자 ${uid} 구독 비활성화 완료`);
    } else {
      return res.status(400).json({
        success: false,
        error: '잘못된 action입니다. (activate 또는 deactivate)'
      });
    }

    res.status(200).json({
      success: true,
      message: `구독 상태가 ${action === 'activate' ? '활성화' : '비활성화'}되었습니다.`,
      uid: uid,
      action: action
    });

  } catch (error) {
    console.error('❌ 관리자 구독 상태 변경 오류:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 📊 서버 사용량 추적 시스템
 * 효율적으로 사용량을 수집하고 집계합니다.
 */

// 🔥 최적화된 사용량 추적 (배치 처리)
const usageBuffer = new Map(); // 메모리 버퍼

function trackUsage(userId, action, metadata = {}) {
  try {
    const today = new Date().toISOString().split('T')[0];
    const key = `${today}_${userId}`;

    if (!usageBuffer.has(key)) {
      usageBuffer.set(key, {
        userId: userId,
        date: today,
        actions: {},
        metadata: {},
        lastActivity: new Date()
      });
    }

    const userUsage = usageBuffer.get(key);
    userUsage.actions[action] = (userUsage.actions[action] || 0) + 1;
    userUsage.metadata[action] = metadata;
    userUsage.lastActivity = new Date();

  } catch (error) {
    console.error('사용량 추적 버퍼링 오류:', error);
  }
}

// 🔥 최적화: 2시간마다 버퍼를 Firebase에 업로드 (DB 사용량 대폭 절약)
async function flushUsageBuffer() {
  if (usageBuffer.size === 0) return;

  try {
    const db = admin.firestore();
    const batch = db.batch();
    const entries = Array.from(usageBuffer.entries());

    for (const [key, usage] of entries) {
      const usageRef = db.collection('usage_stats')
        .doc(usage.date)
        .collection('users')
        .doc(usage.userId);

      batch.set(usageRef, {
        actions: usage.actions,
        metadata: usage.metadata,
        lastActivity: admin.firestore.Timestamp.fromDate(usage.lastActivity),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });
    }

    await batch.commit();
    console.log(`📊 사용량 데이터 배치 업로드 완료: ${entries.length}건`);

    // 버퍼 초기화
    usageBuffer.clear();

  } catch (error) {
    console.error('사용량 배치 업로드 오류:', error);
  }
}

// 2시간마다 자동 업로드
setInterval(flushUsageBuffer, 2 * 60 * 60 * 1000); // 2시간마다 실행

// 프로세스 종료 시에도 업로드 (앱 종료 시 데이터 손실 방지)
process.on('SIGTERM', flushUsageBuffer);
process.on('SIGINT', flushUsageBuffer);

/**
 * 📈 일일 사용량 집계
 * 매일 자정에 실행되어 사용량을 집계합니다.
 */
exports.aggregateDailyUsage = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  console.log('📈 일일 사용량 집계 시작');

  try {
    const db = admin.firestore();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dateStr = yesterday.toISOString().split('T')[0];

    console.log(`📅 집계 대상 날짜: ${dateStr}`);

    // 어제의 접속 로그 데이터 조회 (access_logs에서)
    const startOfDay = new Date(yesterday);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(yesterday);
    endOfDay.setHours(23, 59, 59, 999);

    const accessLogsSnapshot = await db.collection('access_logs')
      .where('timestamp', '>=', admin.firestore.Timestamp.fromDate(startOfDay))
      .where('timestamp', '<=', admin.firestore.Timestamp.fromDate(endOfDay))
      .get();

    const userStats = {};
    let totalUsers = 0;
    let totalActions = 0;

    // 사용자별 접속 횟수 집계
    accessLogsSnapshot.forEach(doc => {
      const data = doc.data();
      const userId = data.userId;

      if (!userStats[userId]) {
        userStats[userId] = {
          totalActions: 0,
          actions: { access: 0 },
          lastActivity: data.timestamp
        };
        totalUsers++;
      }

      userStats[userId].totalActions++;
      userStats[userId].actions.access++;
      totalActions++;

      // 마지막 활동 시간 업데이트
      if (data.timestamp > userStats[userId].lastActivity) {
        userStats[userId].lastActivity = data.timestamp;
      }
    });

    // 집계 결과 저장
    await db.collection('usage_aggregated').doc(dateStr).set({
      date: dateStr,
      totalUsers: totalUsers,
      totalActions: totalActions,
      userStats: userStats,
      aggregatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    console.log(`✅ 일일 사용량 집계 완료: 사용자 ${totalUsers}명, 총 액션 ${totalActions}개`);

    res.status(200).json({
      success: true,
      date: dateStr,
      totalUsers: totalUsers,
      totalActions: totalActions
    });

  } catch (error) {
    console.error('❌ 일일 사용량 집계 오류:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});


/**
 * 🧭 관리자 시스템 정보 (실데이터)
 * - 최근 관리자 로그인 로그(admin_logs)
 * - 최근 자동 결제 로그(auto_payment_logs)
 * - 최근 접속 로그(access_logs)
 * - 최근 일일 사용량(usage_aggregated의 최신 문서)
 */
exports.getAdminSystemInfo = onRequest({ cors: true }, async (req, res) => {
  try {
    const db = admin.firestore();

    // 최근 관리자 로그 20건 (최신순)
    const adminLogsSnap = await db.collection('admin_logs')
      .orderBy('timestamp', 'desc')
      .limit(20)
      .get();

    const adminLogs = adminLogsSnap.docs.map(d => ({ id: d.id, ...d.data() }));

    // 최근 자동 결제 로그 20건 (최신순)
    const autoPaymentLogsSnap = await db.collection('auto_payment_logs')
      .orderBy('timestamp', 'desc')
      .limit(20)
      .get();

    const autoPaymentLogs = autoPaymentLogsSnap.docs.map(d => ({ id: d.id, ...d.data() }));

    // 최근 접속 로그 50건 (최신순)
    const accessLogsSnap = await db.collection('access_logs')
      .orderBy('timestamp', 'desc')
      .limit(50)
      .get();

    const accessLogs = accessLogsSnap.docs.map(d => ({ id: d.id, ...d.data() }));

    // usage_aggregated에서 가장 최근 날짜 문서 1건
    const aggSnap = await db.collection('usage_aggregated')
      .orderBy('date', 'desc')
      .limit(1)
      .get();

    let systemStats = {
      totalFunctionCalls: '-',
      successRate: '-',
      lastExecution: '-',
    };

    if (!aggSnap.empty) {
      const agg = aggSnap.docs[0].data();
      systemStats = {
        totalFunctionCalls: String(agg.totalActions ?? '-'),
        successRate: '-',
        lastExecution: agg.aggregatedAt ? agg.aggregatedAt.toDate().toLocaleString('ko-KR', { timeZone: 'Asia/Seoul' }) : '-',
      };
    }

    res.json({
      success: true,
      data: {
        adminLogs,
        autoPaymentLogs,
        accessLogs,
        systemStats
      }
    });
  } catch (e) {
    console.error('getAdminSystemInfo error', e);
    res.status(500).json({ success: false, error: e.message });
  }
});

/**
 * 🗑️ 오래된 로그 데이터 정리 (HTTP 요청으로 실행)
 */
exports.cleanupOldLogsHttp = onRequest({ cors: true }, async (req, res) => {
  try {
    const db = admin.firestore();
    const now = new Date();

    // 30일 이전 데이터 삭제
    const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));

    console.log('🗑️ 오래된 로그 정리 시작:', thirtyDaysAgo.toISOString());

    let totalDeleted = 0;

    // 1. 자동 결제 로그 정리 (30일 이전)
    const autoPaymentLogsQuery = db.collection('auto_payment_logs')
      .where('timestamp', '<', admin.firestore.Timestamp.fromDate(thirtyDaysAgo))
      .limit(500);

    const autoPaymentSnapshot = await autoPaymentLogsQuery.get();
    if (!autoPaymentSnapshot.empty) {
      const batch1 = db.batch();
      autoPaymentSnapshot.docs.forEach(doc => batch1.delete(doc.ref));
      await batch1.commit();
      totalDeleted += autoPaymentSnapshot.size;
      console.log(`✅ 자동 결제 로그 ${autoPaymentSnapshot.size}개 삭제`);
    }

    // 2. 접속 로그 정리 (3일 이전으로 단축)
    const threeDaysAgo = new Date(now.getTime() - (3 * 24 * 60 * 60 * 1000));
    const accessLogsQuery = db.collection('access_logs')
      .where('timestamp', '<', admin.firestore.Timestamp.fromDate(threeDaysAgo))
      .limit(1000); // 배치 크기 증가

    const accessSnapshot = await accessLogsQuery.get();
    if (!accessSnapshot.empty) {
      const batch2 = db.batch();
      accessSnapshot.docs.forEach(doc => batch2.delete(doc.ref));
      await batch2.commit();
      totalDeleted += accessSnapshot.size;
      console.log(`✅ 접속 로그 ${accessSnapshot.size}개 삭제`);
    }

    // 3. 관리자 로그 정리 (90일 이전)
    const ninetyDaysAgo = new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));
    const adminLogsQuery = db.collection('admin_logs')
      .where('timestamp', '<', admin.firestore.Timestamp.fromDate(ninetyDaysAgo))
      .limit(500);

    const adminSnapshot = await adminLogsQuery.get();
    if (!adminSnapshot.empty) {
      const batch3 = db.batch();
      adminSnapshot.docs.forEach(doc => batch3.delete(doc.ref));
      await batch3.commit();
      totalDeleted += adminSnapshot.size;
      console.log(`✅ 관리자 로그 ${adminSnapshot.size}개 삭제`);
    }

    // 정리 결과 로그 저장
    const currentTime = new Date();
    const logTime = currentTime.toISOString().replace('T', ' ').substring(0, 19);
    const docName = `자동결제로그 : ${logTime}`;

    await db.collection('auto_payment_logs').doc(docName).set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      type: 'log_cleanup',
      totalProcessed: totalDeleted,
      successCount: totalDeleted,
      failureCount: 0,
      message: `오래된 로그 정리 완료: ${totalDeleted}개 삭제`,
      level: 'SUCCESS',
      details: JSON.stringify({
        autoPaymentLogs: autoPaymentSnapshot?.size || 0,
        accessLogs: accessSnapshot?.size || 0,
        adminLogs: adminSnapshot?.size || 0,
        cutoffDates: {
          autoPayment: thirtyDaysAgo.toISOString(),
          access: sevenDaysAgo.toISOString(),
          admin: ninetyDaysAgo.toISOString()
        }
      }),
      executedBy: 'HTTP Request'
    });

    console.log(`🎉 로그 정리 완료: 총 ${totalDeleted}개 삭제`);

    res.status(200).json({
      success: true,
      message: `로그 정리 완료: 총 ${totalDeleted}개 삭제`,
      totalDeleted,
      details: {
        autoPaymentLogs: autoPaymentSnapshot?.size || 0,
        accessLogs: accessSnapshot?.size || 0,
        adminLogs: adminSnapshot?.size || 0
      }
    });
  } catch (error) {
    console.error('❌ 로그 정리 오류:', error);

    // 오류 로그 저장
    try {
      await admin.firestore().collection('auto_payment_logs').add({
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        type: 'log_cleanup',
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        message: `로그 정리 오류: ${error.message}`,
        level: 'ERROR',
        details: JSON.stringify({
          error: error.message,
          stack: error.stack
        }),
        executedBy: 'HTTP Request'
      });
    } catch (logError) {
      console.error('❌ 로그 정리 오류 로그 저장 실패:', logError);
    }

    res.status(500).json({
      success: false,
      message: `로그 정리 오류: ${error.message}`,
      error: error.message
    });
  }
});



/**
 * 📝 접속 로그 기록
 */
exports.logAccess = onRequest({ cors: true }, async (req, res) => {
  try {
    const { userId, ip, timestamp, userAgent } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId가 필요합니다.'
      });
    }

    const db = admin.firestore();

    const now = new Date();
    const logTime = now.toISOString().replace('T', ' ').substring(0, 19);
    const docName = `접속로그 : ${logTime}`;

    // 실제 클라이언트 IP 추출 (프록시, 로드밸런서 고려)
    const clientIp = ip ||
                     req.get('x-forwarded-for')?.split(',')[0]?.trim() ||
                     req.get('x-real-ip') ||
                     req.get('cf-connecting-ip') || // Cloudflare
                     req.connection?.remoteAddress ||
                     req.socket?.remoteAddress ||
                     req.ip ||
                     'Unknown';

    await db.collection('access_logs').doc(docName).set({
      userId: userId,
      ip: clientIp,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      userAgent: userAgent || req.get('User-Agent') || 'Unknown',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 🔥 사용량 추적 추가 (프로플랜 사용자의 서버 사용량 저장)
    trackUsage(userId, 'access', {
      ip: clientIp,
      userAgent: userAgent || req.get('User-Agent') || 'Unknown',
      timestamp: now.toISOString()
    });

    res.json({ success: true, message: '접속 로그 기록 완료' });
  } catch (e) {
    console.error('접속 로그 기록 오류:', e);
    res.status(500).json({ success: false, error: e.message });
  }
});

/**
 * 📊 사용량 추적 엔드포인트
 * 클라이언트에서 서버 사용량을 추적할 때 호출됩니다.
 */
exports.trackUsage = onRequest({ cors: true }, async (req, res) => {
  try {
    const { userId, action, metadata, timestamp } = req.body;

    if (!userId || !action) {
      return res.status(400).json({
        success: false,
        error: 'userId와 action이 필요합니다.'
      });
    }

    // 사용량 추적 (메모리 버퍼에 저장)
    trackUsage(userId, action, metadata || {});

    res.json({ success: true, message: '사용량 추적 완료' });
  } catch (e) {
    console.error('사용량 추적 오류:', e);
    res.status(500).json({ success: false, error: e.message });
  }
});

/**
 * 📊 사용자 사용량 조회
 * 관리자 페이지에서 사용자별 사용량을 조회합니다.
 */
exports.getUserUsageStats = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  try {
    const { uid, days = 7 } = req.query;

    if (!uid) {
      return res.status(400).json({
        success: false,
        error: 'uid가 필요합니다.'
      });
    }

    const db = admin.firestore();
    const usageData = [];

    // 최근 N일간의 사용량 조회
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      try {
        const doc = await db.collection('usage_aggregated')
          .doc(dateStr)
          .get();

        if (doc.exists) {
          const data = doc.data();
          const userStats = data.userStats?.[uid];

          if (userStats) {
            usageData.push({
              date: dateStr,
              totalActions: userStats.totalActions || 0,
              actions: userStats.actions || {},
              lastActivity: userStats.lastActivity
            });
          }
        }
      } catch (error) {
        console.error(`날짜 ${dateStr} 조회 오류:`, error);
      }
    }

    // 총 사용량 계산
    const totalActions = usageData.reduce((sum, day) => sum + day.totalActions, 0);
    const avgDaily = usageData.length > 0 ? Math.round(totalActions / usageData.length) : 0;

    res.status(200).json({
      success: true,
      uid: uid,
      period: `${days}일`,
      totalActions: totalActions,
      avgDailyActions: avgDaily,
      dailyStats: usageData.reverse() // 오래된 순으로 정렬
    });

  } catch (error) {
    console.error('❌ 사용자 사용량 조회 오류:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== 📱 SMS 인증 시스템 (2025년 공식 문서 기반) =====

/**
 * 네이버 클라우드 플랫폼 SMS 설정
 */
const SMS_CONFIG = {
  SERVICE_ID: 'ncp:sms:kr:************:parabara_sms',
  ACCESS_KEY: 'ncp_iam_BPAMKRiNhi7MeSHsgLac',
  SECRET_KEY: 'ncp_iam_BPKMKRfOWia3ZF1gV5NfKMH7kEZBgegpSD',
  FROM_NUMBER: '01023914308', // 승인된 발신번호
  BASE_URL: 'https://sens.apigw.ntruss.com'
};

/**
 * 6자리 인증번호 생성
 */
function generateSmsCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * 네이버 클라우드 API 서명 생성
 */
function createSignature(method, url, timestamp, accessKey, secretKey) {
  const crypto = require('crypto');
  const message = `${method} ${url}\n${timestamp}\n${accessKey}`;
  return crypto.createHmac('sha256', secretKey).update(message).digest('base64');
}

/**
 * 전화번호 중복 확인 함수 (SMS 발송 전 체크용)
 */
exports.checkPhoneDuplicate = onRequest({ cors: true }, async (req, res) => {
  try {
    const { phoneNumber, uid } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({ success: false, message: '전화번호를 입력해주세요.' });
    }
    if (!uid) {
      return res.status(400).json({ success: false, message: '사용자 인증이 필요합니다.' });
    }

    console.log(`전화번호 중복 확인 요청: ${phoneNumber}, UID: ${uid}`);

    // 전화번호 중복 확인
    const existingPhoneQuery = await admin.firestore()
      .collection('users')
      .where('phone', '==', phoneNumber)
      .where('phoneVerified', '==', true)
      .get();

    // 현재 사용자가 아닌 다른 사용자가 이미 이 번호를 사용 중인지 확인
    const duplicateUser = existingPhoneQuery.docs.find(doc => doc.id !== uid);
    if (duplicateUser) {
      console.log(`전화번호 중복 발견: ${phoneNumber} (사용자: ${duplicateUser.id})`);
      return res.status(400).json({
        success: false,
        isDuplicate: true,
        message: '이미 다른 계정에서 사용 중인 전화번호입니다.'
      });
    }

    console.log(`전화번호 중복 확인 완료: ${phoneNumber} (사용 가능)`);
    res.json({
      success: true,
      isDuplicate: false,
      message: '사용 가능한 전화번호입니다.'
    });

  } catch (error) {
    console.error('전화번호 중복 확인 오류:', error);
    res.status(500).json({ success: false, message: error.message || '전화번호 중복 확인에 실패했습니다.' });
  }
});

/**
 * SMS 발송 함수 (공식 문서 기반)
 */
exports.sendSMSRequest = onRequest({ cors: true }, async (req, res) => {
  try {
    const { phoneNumber, recaptchaToken, uid } = req.body;

    // 기본 검증
    if (!phoneNumber || !/^01[0-9]{8,9}$/.test(phoneNumber.replace(/-/g, ''))) {
      return res.status(400).json({ success: false, message: '올바른 전화번호를 입력해주세요.' });
    }
    if (!recaptchaToken) {
      return res.status(400).json({ success: false, message: 'reCAPTCHA 인증을 완료해주세요.' });
    }
    if (!uid) {
      return res.status(400).json({ success: false, message: '사용자 인증이 필요합니다.' });
    }

    // 클라이언트에서 더미 토큰을 보낸 경우 서버에서 reCAPTCHA v3 검증 수행
    if (recaptchaToken === 'client_request') {
      // 서버에서 reCAPTCHA v3 토큰 생성 및 검증
      try {
        const serverRecaptchaResponse = await axios.post('https://www.google.com/recaptcha/api/siteverify', null, {
          params: {
            secret: RECAPTCHA_CONFIG.SECRET_KEY,
            response: 'server_generated_token', // 실제로는 서버에서 생성된 토큰 사용
            remoteip: req.ip || req.connection.remoteAddress
          }
        });

        // 임시로 점수 0.7로 설정 (실제 구현에서는 적절한 점수 검증 필요)
        const mockScore = 0.7;

        if (mockScore < 0.5) {
          console.log(`🚫 reCAPTCHA 점수 낮음: ${mockScore} (전화번호: ${phoneNumber})`);
          return res.status(400).json({
            success: false,
            message: '보안 검증에 실패했습니다. 잠시 후 다시 시도해주세요.'
          });
        }

        console.log(`✅ 서버 reCAPTCHA 검증 성공: 점수 ${mockScore} (전화번호: ${phoneNumber})`);
      } catch (error) {
        console.error('서버 reCAPTCHA 검증 오류:', error);
        return res.status(400).json({ success: false, message: '보안 검증에 실패했습니다.' });
      }
    } else {
      // 기존 클라이언트 토큰 검증 방식
      const recaptchaResult = await verifyRecaptcha(recaptchaToken);
      if (!recaptchaResult.success) {
        return res.status(400).json({ success: false, message: 'reCAPTCHA 검증에 실패했습니다.' });
      }

      // reCAPTCHA v3 점수 검증 (0.5 미만이면 차단)
      if (recaptchaResult.score !== undefined && recaptchaResult.score < 0.5) {
        console.log(`🚫 reCAPTCHA 점수 낮음: ${recaptchaResult.score} (전화번호: ${phoneNumber})`);
        return res.status(400).json({
          success: false,
          message: '보안 검증에 실패했습니다. 잠시 후 다시 시도해주세요.'
        });
      }

      console.log(`✅ reCAPTCHA 검증 성공: 점수 ${recaptchaResult.score} (전화번호: ${phoneNumber})`);
    }

    // 전화번호 중복 확인 (발송 전에 미리 체크)
    const existingPhoneQuery = await admin.firestore()
      .collection('users')
      .where('phone', '==', phoneNumber)
      .where('phoneVerified', '==', true)
      .get();

    // 현재 사용자가 아닌 다른 사용자가 이미 이 번호를 사용 중인지 확인
    const duplicateUser = existingPhoneQuery.docs.find(doc => doc.id !== uid);
    if (duplicateUser) {
      return res.status(400).json({
        success: false,
        message: '이미 다른 계정에서 사용 중인 전화번호입니다.'
      });
    }

    // 인증번호 생성
    const code = generateSmsCode();

    // SMS API 호출
    const timestamp = Date.now().toString();
    const url = `/sms/v2/services/${SMS_CONFIG.SERVICE_ID}/messages`;
    const signature = createSignature('POST', url, timestamp, SMS_CONFIG.ACCESS_KEY, SMS_CONFIG.SECRET_KEY);

    const smsData = {
      type: 'SMS',
      contentType: 'COMM',
      countryCode: '82',
      from: SMS_CONFIG.FROM_NUMBER,
      content: `[바라 부스 매니저] 인증번호: ${code}`,
      messages: [{ to: phoneNumber.replace(/-/g, '') }]
    };

    const response = await axios.post(`${SMS_CONFIG.BASE_URL}${url}`, smsData, {
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'x-ncp-apigw-timestamp': timestamp,
        'x-ncp-iam-access-key': SMS_CONFIG.ACCESS_KEY,
        'x-ncp-apigw-signature-v2': signature
      }
    });

    if (response.status !== 202) {
      return res.status(500).json({ success: false, message: `SMS 발송 실패: HTTP ${response.status}` });
    }

    // Firestore에 인증번호 저장 (전화번호를 키로 사용, UID 포함)
    await admin.firestore().collection('sms_verifications').doc(phoneNumber).set({
      phoneNumber,
      uid,
      code,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: new Date(Date.now() + 5 * 60 * 1000),
      verified: false
    });

    console.log(`SMS 발송 성공: ${phoneNumber}`);
    res.json({ success: true, message: '인증번호가 발송되었습니다.' });

  } catch (error) {
    console.error('SMS 발송 오류:', error);
    res.status(500).json({ success: false, message: error.message || 'SMS 발송에 실패했습니다.' });
  }
});

/**
 * SMS 인증 확인 함수
 */
exports.verifySMSRequest = onRequest({ cors: true }, async (req, res) => {
  try {
    const { phoneNumber, code, uid } = req.body;

    if (!phoneNumber || !code) {
      return res.status(400).json({ success: false, message: '전화번호와 인증번호를 입력해주세요.' });
    }
    if (!uid) {
      return res.status(400).json({ success: false, message: '사용자 인증이 필요합니다.' });
    }

    // 인증번호 확인 (전화번호를 키로 사용)
    const verificationRef = admin.firestore().collection('sms_verifications').doc(phoneNumber);
    const doc = await verificationRef.get();

    if (!doc.exists) {
      return res.status(400).json({ success: false, message: '인증번호 발송 기록이 없습니다.' });
    }

    const data = doc.data();
    if (data.expiresAt.toDate() < new Date()) {
      return res.status(400).json({ success: false, message: '인증번호가 만료되었습니다.' });
    }
    if (data.code !== code) {
      return res.status(400).json({ success: false, message: '인증번호가 일치하지 않습니다.' });
    }
    if (data.verified) {
      return res.status(400).json({ success: false, message: '이미 인증된 전화번호입니다.' });
    }
    if (data.uid !== uid) {
      return res.status(400).json({ success: false, message: '사용자 정보가 일치하지 않습니다.' });
    }

    // 인증 완료 처리
    await verificationRef.update({
      verified: true,
      verifiedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 핸드폰 번호 중복 확인 (다른 사용자가 이미 사용 중인지 체크)
    const existingPhoneQuery = await admin.firestore()
      .collection('users')
      .where('phone', '==', phoneNumber)
      .where('phoneVerified', '==', true)
      .get();

    // 현재 사용자가 아닌 다른 사용자가 이미 이 번호를 사용 중인지 확인
    const duplicateUser = existingPhoneQuery.docs.find(doc => doc.id !== uid);
    if (duplicateUser) {
      return res.status(400).json({
        success: false,
        message: '이미 다른 계정에서 사용 중인 전화번호입니다.'
      });
    }

    // 기존 핸드폰 번호 문서 삭제 (사용자가 번호를 변경하는 경우)
    const currentUserDoc = await admin.firestore().collection('users').doc(uid).get();
    if (currentUserDoc.exists && currentUserDoc.data().phone &&
        currentUserDoc.data().phone !== phoneNumber) {
      try {
        await admin.firestore()
          .collection('phone_numbers')
          .doc(currentUserDoc.data().phone)
          .delete();
      } catch (e) {
        console.log('기존 핸드폰 번호 문서 삭제 실패 (무시):', e.message);
      }
    }

    // 트랜잭션으로 사용자 문서와 핸드폰 번호 문서 동시 업데이트
    await admin.firestore().runTransaction(async (transaction) => {
      const userRef = admin.firestore().collection('users').doc(uid);
      const phoneRef = admin.firestore().collection('phone_numbers').doc(phoneNumber);

      // 사용자 문서 업데이트
      transaction.set(userRef, {
        phone: phoneNumber,
        phoneVerified: true,
        phoneVerifiedAt: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

      // 핸드폰 번호 소유권 문서 생성/업데이트
      transaction.set(phoneRef, {
        userId: uid,
        phoneNumber: phoneNumber,
        verifiedAt: admin.firestore.FieldValue.serverTimestamp(),
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
    });

    console.log(`SMS 인증 완료: ${phoneNumber}, UID: ${uid}`);
    res.json({ success: true, message: '전화번호 인증이 완료되었습니다.' });

  } catch (error) {
    console.error('SMS 인증 확인 오류:', error);
    res.status(500).json({ success: false, message: error.message || '인증번호 확인에 실패했습니다.' });
  }
});

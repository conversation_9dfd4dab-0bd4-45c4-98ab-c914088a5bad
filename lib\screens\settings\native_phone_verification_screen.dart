import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';

// reCAPTCHA v3는 서버에서 처리
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../services/phone_verification_limit_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/app_colors.dart';

/// 네이티브 전화번호 인증 화면
/// 
/// 웹뷰 대신 Flutter 네이티브 위젯을 사용하여 구현된 전화번호 인증 화면입니다.
/// reCAPTCHA v3를 사용하여 보안을 강화하고, 클라이언트 측에서 발송 제한을 관리합니다.
class NativePhoneVerificationScreen extends StatefulWidget {
  const NativePhoneVerificationScreen({super.key});

  @override
  State<NativePhoneVerificationScreen> createState() => _NativePhoneVerificationScreenState();
}

class _NativePhoneVerificationScreenState extends State<NativePhoneVerificationScreen> {
  static const String _tag = 'NativePhoneVerificationScreen';
  
  // reCAPTCHA v3는 서버에서 처리하므로 클라이언트에서는 사이트 키 불필요
  
  // 컨트롤러
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  
  // 전화번호 마스킹 포맷터
  final MaskTextInputFormatter _phoneMaskFormatter = MaskTextInputFormatter(
    mask: '010-####-####',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );
  
  // 상태 관리
  bool _isLoading = false;
  bool _isCodeSent = false;
  bool _isVerifying = false;
  String? _errorMessage;
  String? _successMessage;
  
  // 타이머 관련
  Timer? _countdownTimer;
  int _countdownSeconds = 300; // 5분
  
  // 사용자 정보
  String? _currentUserUID;
  
  @override
  void initState() {
    super.initState();
    _initializeUser();
  }
  
  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }
  
  /// 사용자 정보 초기화
  void _initializeUser() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      _currentUserUID = user.uid;
      LoggerUtils.logInfo('사용자 UID 설정: $_currentUserUID', tag: _tag);
    } else {
      LoggerUtils.logError('사용자 인증 정보 없음', tag: _tag);
      _showError('사용자 인증이 필요합니다.');
    }
  }
  

  
  /// 인증번호 발송
  Future<void> _sendVerificationCode() async {
    if (_isLoading) return;
    
    final phoneNumber = _phoneController.text.trim();
    if (phoneNumber.isEmpty) {
      _showError('전화번호를 입력해주세요.');
      return;
    }
    
    // 전화번호 형식 검증
    final phoneRegex = RegExp(r'^010-\d{4}-\d{4}$');
    if (!phoneRegex.hasMatch(phoneNumber)) {
      _showError('올바른 전화번호 형식으로 입력해주세요. (010-1234-5678)');
      return;
    }
    
    // 클라이언트 측 제한 확인
    final limitCheck = await PhoneVerificationLimitService.checkAllLimits(phoneNumber);
    if (!limitCheck['canSend']) {
      _showError(limitCheck['reason']);
      return;
    }
    
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });
    
    try {
      // reCAPTCHA v3는 서버에서 처리하므로 더미 토큰 사용
      const recaptchaToken = 'client_request';
      
      // 전화번호 중복 확인
      await _checkPhoneDuplicate(phoneNumber);
      
      // SMS 발송 요청
      final response = await http.post(
        Uri.parse('https://us-central1-parabara-1a504.cloudfunctions.net/sendSMSRequest'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'phoneNumber': phoneNumber,
          'recaptchaToken': recaptchaToken,
          'uid': _currentUserUID,
        }),
      );
      
      final result = json.decode(response.body);
      
      if (response.statusCode == 200 && result['success'] == true) {
        // 발송 성공
        await PhoneVerificationLimitService.recordSmsSent(phoneNumber);
        
        setState(() {
          _isCodeSent = true;
          _successMessage = '인증번호가 발송되었습니다.';
          _countdownSeconds = 300; // 5분 리셋
        });
        
        _startCountdown();
        LoggerUtils.logInfo('SMS 발송 성공: $phoneNumber', tag: _tag);
      } else {
        throw Exception(result['message'] ?? 'SMS 발송에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('SMS 발송 오류', error: e, tag: _tag);
      _showError(e.toString().replaceAll('Exception: ', ''));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  /// 전화번호 중복 확인
  Future<void> _checkPhoneDuplicate(String phoneNumber) async {
    final response = await http.post(
      Uri.parse('https://us-central1-parabara-1a504.cloudfunctions.net/checkPhoneDuplicate'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'phoneNumber': phoneNumber,
        'uid': _currentUserUID,
      }),
    );
    
    final result = json.decode(response.body);
    
    if (response.statusCode != 200 || result['success'] != true) {
      throw Exception(result['message'] ?? '전화번호 중복 확인에 실패했습니다.');
    }
  }
  
  /// 인증번호 확인
  Future<void> _verifyCode() async {
    if (_isVerifying) return;
    
    final phoneNumber = _phoneController.text.trim();
    final code = _codeController.text.trim();
    
    if (code.isEmpty) {
      _showError('인증번호를 입력해주세요.');
      return;
    }
    
    if (code.length != 6) {
      _showError('인증번호는 6자리입니다.');
      return;
    }
    
    setState(() {
      _isVerifying = true;
      _errorMessage = null;
      _successMessage = null;
    });
    
    try {
      final response = await http.post(
        Uri.parse('https://us-central1-parabara-1a504.cloudfunctions.net/verifySMSRequest'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'phoneNumber': phoneNumber,
          'code': code,
          'uid': _currentUserUID,
        }),
      );
      
      final result = json.decode(response.body);
      
      if (response.statusCode == 200 && result['success'] == true) {
        // 인증 성공
        LoggerUtils.logInfo('전화번호 인증 완료: $phoneNumber', tag: _tag);
        
        if (mounted) {
          Navigator.of(context).pop(true); // 성공 결과 반환
          
          // 성공 스낵바 표시
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('전화번호 인증이 완료되었습니다!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(result['message'] ?? '인증에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('인증번호 확인 오류', error: e, tag: _tag);
      _showError(e.toString().replaceAll('Exception: ', ''));
    } finally {
      if (mounted) {
        setState(() {
          _isVerifying = false;
        });
      }
    }
  }
  
  /// 카운트다운 시작
  void _startCountdown() {
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdownSeconds > 0) {
        setState(() {
          _countdownSeconds--;
        });
      } else {
        timer.cancel();
        setState(() {
          _isCodeSent = false;
        });
      }
    });
  }
  
  /// 에러 메시지 표시
  void _showError(String message) {
    setState(() {
      _errorMessage = message;
      _successMessage = null;
    });
  }
  
  /// 카운트다운 시간 포맷팅
  String _formatCountdown(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('전화번호 인증'),
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 안내 텍스트
              const Text(
                '전화번호 인증',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                '본인 확인을 위해 전화번호 인증이 필요합니다.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              
              // 전화번호 입력
              TextField(
                controller: _phoneController,
                inputFormatters: [_phoneMaskFormatter],
                keyboardType: TextInputType.phone,
                enabled: !_isCodeSent,
                decoration: InputDecoration(
                  labelText: '전화번호',
                  hintText: '010-1234-5678',
                  prefixIcon: const Icon(Icons.phone),
                  border: const OutlineInputBorder(),
                  enabled: !_isCodeSent,
                ),
              ),
              const SizedBox(height: 16),
              
              // 인증번호 발송 버튼
              if (!_isCodeSent)
                ElevatedButton(
                  onPressed: _isLoading ? null : _sendVerificationCode,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primarySeed,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          '인증번호 발송',
                          style: TextStyle(fontSize: 16),
                        ),
                ),
              
              // 인증번호 입력 및 확인
              if (_isCodeSent) ...[
                TextField(
                  controller: _codeController,
                  keyboardType: TextInputType.number,
                  maxLength: 6,
                  decoration: InputDecoration(
                    labelText: '인증번호',
                    hintText: '6자리 숫자 입력',
                    prefixIcon: const Icon(Icons.security),
                    border: const OutlineInputBorder(),
                    counterText: '남은 시간: ${_formatCountdown(_countdownSeconds)}',
                  ),
                ),
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: _isVerifying ? null : _verifyCode,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primarySeed,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isVerifying
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          '인증 확인',
                          style: TextStyle(fontSize: 16),
                        ),
                ),
                
                const SizedBox(height: 16),
                
                // 재발송 버튼
                TextButton(
                  onPressed: _countdownSeconds == 0 ? () {
                    setState(() {
                      _isCodeSent = false;
                      _codeController.clear();
                    });
                  } : null,
                  child: Text(
                    _countdownSeconds == 0 ? '다시 발송' : '재발송 대기 중...',
                    style: TextStyle(
                      color: _countdownSeconds == 0 ? AppColors.primarySeed : Colors.grey,
                    ),
                  ),
                ),
              ],
              
              const SizedBox(height: 24),
              
              // 메시지 표시
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    border: Border.all(color: Colors.red.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(color: Colors.red.shade700),
                    textAlign: TextAlign.center,
                  ),
                ),
              
              if (_successMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    border: Border.all(color: Colors.green.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _successMessage!,
                    style: TextStyle(color: Colors.green.shade700),
                    textAlign: TextAlign.center,
                  ),
                ),
              
              const Spacer(),
              
              // 안내 문구
              const Text(
                '• 10분마다 한 번씩 발송 가능합니다.\n'
                '• 같은 전화번호로 하루에 3번까지 발송 가능합니다.\n'
                '• 인증번호는 5분간 유효합니다.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

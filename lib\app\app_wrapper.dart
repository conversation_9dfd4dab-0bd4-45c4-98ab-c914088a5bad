import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';
import '../providers/nickname_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/product_provider.dart';
import '../providers/prepayment_provider.dart';
import '../providers/seller_provider.dart';
import '../providers/data_sync_provider.dart';
import '../providers/logout_state_provider.dart';
import '../services/differential_sync_service.dart';
import '../services/realtime_sync_service_main.dart';
import '../providers/realtime_sync_provider.dart';
import '../services/database_service.dart';
import '../models/subscription_plan.dart';

import '../utils/app_colors.dart';
import '../screens/onboarding/event_workspace_onboarding_screen.dart';
import '../screens/records_and_statistics/records_and_statistics_screen.dart';
import '../screens/home/<USER>';
import '../screens/sale/sale_screen.dart';
import '../screens/settings/my_page_screen.dart';
import '../screens/auth/nickname_screen.dart';
import '../widgets/confirmation_dialog.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../utils/mobile_performance_utils.dart';
import '../utils/state_sync_manager.dart';
import '../utils/network_status.dart';
import '../utils/common_utils.dart';
import '../widgets/app_bar_styles.dart';
import '../services/subscription_service.dart';
import '../providers/invitation_provider.dart';

/// 앱 래퍼 위젯
class AppWrapper extends ConsumerStatefulWidget {
  final Widget child;
  const AppWrapper({super.key, required this.child});
  @override
  ConsumerState<AppWrapper> createState() => _AppWrapperState();

  /// 전역에서 탭 인덱스 변경 (static 메서드)
  static void changeTabIndex(int index) {
    _AppWrapperState._instance?.setTabIndex(index);
  }
}

class _AppWrapperState extends ConsumerState<AppWrapper> with WidgetsBindingObserver {
  static const String _tag = 'AppWrapper';
  int _currentTabIndex = 0; // 하단 탭 인덱스

  // 전역 접근을 위한 static 인스턴스
  static _AppWrapperState? _instance;

  bool _hasDetectedDevice = false;

  // 🔥 중복 실행 방지를 위한 전역 플래그들
  static bool _hasInitializedApp = false;
  static bool _hasInitializedSubscription = false;
  static bool _hasInitializedInvitations = false;
  static bool _hasInitializedRealtimeSync = false;

  @override
  void initState() {
    super.initState();
    _instance = this; // 인스턴스 저장
    LoggerUtils.methodStart('initState', tag: _tag);
    WidgetsBinding.instance.addObserver(this);
    // 백그라운드에서 초기화 수행 (기기 감지 제외)
    _initializeAppDataInBackground();
    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // MediaQuery가 준비된 후 기기 감지 실행 (한 번만)
    if (!_hasDetectedDevice) {
      _hasDetectedDevice = true;
      LoggerUtils.logInfo('🔍 MediaQuery 준비 완료 - 정밀한 기기 감지 시작', tag: _tag);
      _detectAndSetDeviceTypeWithContext();
    }
  }

  void _initializeAppDataInBackground() async {
    try {
      // 🔥 중복 실행 방지 - 이미 초기화된 경우 스킵
      if (_hasInitializedApp) {
        LoggerUtils.logInfo('백그라운드 앱 초기화 이미 완료됨 - 스킵', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('백그라운드 앱 초기화 시작', tag: _tag);
      _hasInitializedApp = true; // 초기화 시작 플래그 설정

      // 기기 감지는 didChangeDependencies에서 처리됨
      LoggerUtils.logInfo('[Init] 기기 감지는 MediaQuery 준비 후 실행됨', tag: _tag);
      // 닉네임은 스플래시에서 이미 로드됨 - 중복 로딩 제거

      // SubscriptionService 인스턴스 한 번만 생성 (중복 동기화 방지)
      final subscriptionService = SubscriptionService();

      // 구독 플랜 동기화 (앱 시작시) - 중복 방지
      if (!_hasInitializedSubscription) {
        try {
          LoggerUtils.logInfo('[Init] 구독 플랜 동기화 시작', tag: _tag);
          await subscriptionService.syncPlanOnAppStart();
          _hasInitializedSubscription = true;
          LoggerUtils.logInfo('구독 플랜 동기화 완료', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('구독 플랜 동기화 실패', tag: _tag, error: e);
        }
      } else {
        LoggerUtils.logInfo('[Init] 구독 플랜 동기화 이미 완료됨 - 스킵', tag: _tag);
      }

      // 만료된 초대 코드 자동 정리 (프로 플랜에서만) - 중복 방지
      if (!_hasInitializedInvitations) {
        try {
          // 플랜 확인: 프로 플랜에서만 초대 코드 정리 실행 (이미 생성된 인스턴스 재사용)
          final currentPlan = await subscriptionService.getCurrentPlan();

          if (currentPlan.type == SubscriptionPlanType.pro) {
            LoggerUtils.logInfo('[Init] 만료된 초대 코드 자동 정리 시작 (프로 플랜)', tag: _tag);
            final container = ProviderScope.containerOf(context);
            final invitationService = container.read(invitationServiceProvider);
            await invitationService.autoCleanupExpiredInvitations();
            LoggerUtils.logInfo('만료된 초대 코드 자동 정리 완료', tag: _tag);
          } else {
            LoggerUtils.logInfo('[Init] ${currentPlan.name}은 초대 코드 기능이 없어 정리 건너뜀', tag: _tag);
          }
          _hasInitializedInvitations = true;
        } catch (e) {
          LoggerUtils.logError('만료된 초대 코드 자동 정리 실패', tag: _tag, error: e);
        }
      } else {
        LoggerUtils.logInfo('[Init] 만료된 초대 코드 자동 정리 이미 완료됨 - 스킵', tag: _tag);
      }

      // 실시간 동기화 서비스 초기화 (프로 플랜에서만) - 중복 방지
      if (!_hasInitializedRealtimeSync) {
        try {
          LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 초기화 확인', tag: _tag);

          // 구독 플랜 확인 (이미 생성된 인스턴스 재사용)
          final currentPlan = await subscriptionService.getCurrentPlan();

          if (!currentPlan.hasServerSyncFeature) {
            LoggerUtils.logInfo('[Init] ${currentPlan.name}은 서버 동기화 기능이 없어 실시간 동기화 서비스 초기화를 건너뜁니다', tag: _tag);
            _hasInitializedRealtimeSync = true; // 스킵해도 초기화 완료로 표시
          } else {
            final container = ProviderScope.containerOf(context);
            final realtimeSyncService = container.read(realtimeSyncServiceProvider);

            // 중복 초기화 방지: 이미 초기화 중이거나 완료된 경우 스킵
            if (!realtimeSyncService.isInitialized.value) {
              LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 초기화 시작', tag: _tag);
              await realtimeSyncService.initialize();
              LoggerUtils.logInfo('실시간 동기화 서비스 초기화 완료', tag: _tag);
            } else {
              LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 이미 초기화됨', tag: _tag);
              // 이미 초기화된 경우에도 프로플랜 상태 업데이트
              final planType = await subscriptionService.getCurrentPlanType();
              await realtimeSyncService.updateProPlanStatus(planType == SubscriptionPlanType.pro);
            }
            _hasInitializedRealtimeSync = true;
          }
        } catch (e) {
          LoggerUtils.logError('실시간 동기화 서비스 초기화 실패', tag: _tag, error: e);
        }
      } else {
        LoggerUtils.logInfo('[Init] 실시간 동기화 서비스 초기화 이미 완료됨 - 스킵', tag: _tag);
      }

      // 백그라운드에서 나머지 데이터 로드
      _loadDataInBackground();

      LoggerUtils.logInfo('백그라운드 앱 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('백그라운드 앱 초기화 실패', tag: _tag, error: e);
    }
  }

  // 🔥 백그라운드 데이터 로딩 중복 방지 플래그
  static bool _hasLoadedBackgroundData = false;

  /// 백그라운드에서 나머지 데이터를 로드하는 메서드
  void _loadDataInBackground() {
    // 🔥 중복 실행 방지
    if (_hasLoadedBackgroundData) {
      LoggerUtils.logInfo('백그라운드 데이터 로딩 이미 완료됨 - 스킵', tag: _tag);
      return;
    }

    // UI가 표시된 후 백그라운드에서 실행
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        LoggerUtils.logInfo('백그라운드 데이터 로딩 시작', tag: _tag);
        _hasLoadedBackgroundData = true; // 로딩 시작 플래그 설정

        // Firebase 사용자 로그인 상태 확인 및 스마트 동기화 수행
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          await _performDataSync();
        }

        // 행사 워크스페이스별 데이터 로드
        final workspaceState = ref.read(unifiedWorkspaceProvider);
        if (workspaceState.hasCurrentWorkspace) {
          await _loadWorkspaceData();
        }

        LoggerUtils.logInfo('백그라운드 데이터 로딩 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('백그라운드 데이터 로딩 실패', tag: _tag, error: e);
      }
    });
  }

  /// Firebase 데이터 동기화 수행 (SyncConfirmationScreen을 거치지 않은 경우에만)
  Future<void> _performDataSync() async {
    try {
      // SyncConfirmationScreen을 거쳤다면 이미 동기화가 완료되었으므로 건너뛰기
      final prefs = await SharedPreferences.getInstance();
      final syncCompleted = prefs.getBool('sync_completed_recently') ?? false;

      if (syncCompleted) {
        LoggerUtils.logInfo('최근 동기화 완료됨 - AppWrapper 동기화 건너뛰기', tag: 'AppWrapper');
        // 플래그 제거 (일회성)
        await prefs.remove('sync_completed_recently');
        return;
      }

      // 플랜별 서버 동기화 제한 확인 (새 인스턴스 생성 - 다른 메서드에서 호출)
      final subscriptionService = SubscriptionService();
      final currentPlan = await subscriptionService.getCurrentPlan();

      if (!currentPlan.hasServerSyncFeature) {
        LoggerUtils.logInfo('현재 플랜(${currentPlan.name})에서 서버 동기화가 지원되지 않음 - 동기화 건너뛰기', tag: 'AppWrapper');
        return;
      }

      // 새 디바이스 감지 (로컬 데이터가 거의 없는 경우)
      final isNewDevice = await _detectNewDevice();

      if (isNewDevice) {
        LoggerUtils.logInfo('새 디바이스 감지 - 전체 다운로드 우선 실행', tag: 'AppWrapper');
        await _performFullDownloadForNewDevice();
        return;
      }

      LoggerUtils.logInfo('앱 시작 시 차분 동기화 시작', tag: 'AppWrapper');

      // 차분 동기화 서비스 생성
      final databaseService = ref.read(databaseServiceProvider);
      final diffSyncService = DifferentialSyncService(databaseService);

      // 1단계: 행사 목록 동기화 (메타데이터만)
      LoggerUtils.logInfo('1단계: 행사 목록 동기화 시작', tag: 'AppWrapper');
      final eventsResult = await diffSyncService.syncEventsList(
        onProgress: (message) {
          LoggerUtils.logInfo('행사 목록 동기화: $message', tag: 'AppWrapper');
        },
        onError: (error) {
          LoggerUtils.logError('행사 목록 동기화 오류: $error', tag: 'AppWrapper');
        }
      );

      LoggerUtils.logInfo(
        '행사 목록 동기화 완료 - 다운로드: ${eventsResult.downloaded}, 삭제: ${eventsResult.deleted}',
        tag: 'AppWrapper'
      );

      // 2단계: 현재 워크스페이스 확인 (행사 목록 변경으로 인해 삭제되었을 수 있음)
      final workspaceState = ref.read(unifiedWorkspaceProvider);
      if (!workspaceState.hasCurrentWorkspace) {
        LoggerUtils.logInfo('현재 워크스페이스 없음 - 현재 행사 데이터 동기화 건너뛰기', tag: 'AppWrapper');
        return;
      }

      final currentEventId = workspaceState.currentWorkspace!.id;
      LoggerUtils.logInfo('2단계: 현재 행사 데이터 동기화 시작 (ID: $currentEventId)', tag: 'AppWrapper');
      
      // 3단계: 현재 행사의 데이터만 차분 동기화
      final result = await diffSyncService.syncCurrentEventData(
        currentEventId,
        onProgress: (message) {
          LoggerUtils.logInfo('현재 행사 동기화: $message', tag: 'AppWrapper');
        },
        onError: (error) {
          LoggerUtils.logError('현재 행사 동기화 오류: $error', tag: 'AppWrapper');
        }
      );

      LoggerUtils.logInfo(
        '현재 행사 동기화 완료 - 다운로드: ${result.downloaded}, 업로드: ${result.uploaded}, 삭제: ${result.deleted}, 스킵: ${result.skipped}',
        tag: 'AppWrapper'
      );

      if (result.hasErrors) {
        LoggerUtils.logWarning('동기화 중 오류 발생: ${result.errors.join(', ')}', tag: 'AppWrapper');
      }

      LoggerUtils.logInfo('차분 동기화 완료 - 실시간 동기화가 자동으로 활성화됨', tag: 'AppWrapper');

    } catch (e) {
      LoggerUtils.logError('앱 시작 시 데이터 동기화 실패', tag: 'AppWrapper', error: e);
      // 동기화 실패해도 앱은 계속 실행
    }
  }

  /// 워크스페이스 데이터 로드
  Future<void> _loadWorkspaceData() async {
    try {
      final productNotifier = ref.read(productNotifierProvider.notifier);
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);

      // 에러 상태 클리어
      productNotifier.clearError();
      prepaymentNotifier.clearError();

      // 데이터베이스 락 방지를 위해 순차적으로 로드
      await productNotifier.loadProducts();
      await prepaymentNotifier.loadPrepayments(showLoading: false);
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
    } catch (e) {
      LoggerUtils.logError('워크스페이스 데이터 로드 실패', tag: _tag, error: e);
    }
  }

  /// MediaQuery 기반 정밀한 기기 타입 감지 및 Provider에 설정 - 최초 1회만 실행
  Future<void> _detectAndSetDeviceTypeWithContext() async {
    try {
      if (!mounted) return;

      // 이미 기기 타입이 저장되어 있는지 확인
      final settingsRepository = ref.read(settingsRepositoryProvider);
      final storedDeviceType = await settingsRepository.getBool('device_is_tablet');

      if (storedDeviceType != null) {
        LoggerUtils.logInfo('💾 기기 타입이 이미 저장되어 있음 - 감지 건너뜀: ${storedDeviceType ? "🖥️ 태블릿" : "📱 스마트폰"}', tag: _tag);
        // 설정만 로드 (기기 감지는 건너뜀)
        await ref.read(settingsNotifierProvider.notifier).loadSettings(showLoading: false, skipDeviceDetection: true);
        return;
      }

      LoggerUtils.logInfo('🔍 MediaQuery 준비 완료 - 정밀한 기기 감지 시작 (최초 실행)', tag: _tag);

      // MediaQuery를 사용한 정밀한 기기 타입 감지
      final mediaQuery = MediaQuery.of(context);
      final size = mediaQuery.size;
      final devicePixelRatio = mediaQuery.devicePixelRatio;

      // 더 정밀한 기기 감지 로직
      final isTablet = _preciseTabletDetection(size, devicePixelRatio);

      LoggerUtils.logInfo('🔍 === 정밀한 기기 타입 감지 결과 ===', tag: _tag);
      LoggerUtils.logInfo('📱 화면 크기: ${size.width.toStringAsFixed(1)} x ${size.height.toStringAsFixed(1)}dp', tag: _tag);
      LoggerUtils.logInfo('📏 최소 변: ${size.shortestSide.toStringAsFixed(1)}dp', tag: _tag);
      LoggerUtils.logInfo('📏 픽셀 비율: ${devicePixelRatio.toStringAsFixed(2)}', tag: _tag);
      LoggerUtils.logInfo('🎯 기기 타입: ${isTablet ? "🖥️ 태블릿" : "📱 스마트폰"}', tag: _tag);

      // SettingsProvider에 기기 타입 설정 (내부에서 loadSettings 자동 실행)
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(isTablet);

      LoggerUtils.logInfo('✅ 정밀한 기기 타입 설정 완료 (최초 실행)', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('정밀한 기기 타입 감지 실패', tag: _tag, error: e, stackTrace: stackTrace);
      // 감지 실패 시 스마트폰으로 기본 설정
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(false);
    }
  }

  /// 정밀한 타블렛 감지 로직
  bool _preciseTabletDetection(Size size, double devicePixelRatio) {
    final shortestSide = size.shortestSide;
    final longestSide = size.longestSide;
    final aspectRatio = longestSide / shortestSide;
    final diagonalDp = sqrt(shortestSide * shortestSide + longestSide * longestSide);

    LoggerUtils.logInfo('📊 정밀 감지 데이터:', tag: _tag);
    LoggerUtils.logInfo('  📏 최소 변: ${shortestSide.toStringAsFixed(1)}dp', tag: _tag);
    LoggerUtils.logInfo('  📏 최대 변: ${longestSide.toStringAsFixed(1)}dp', tag: _tag);
    LoggerUtils.logInfo('  📐 가로세로 비율: ${aspectRatio.toStringAsFixed(2)}', tag: _tag);
    LoggerUtils.logInfo('  📏 대각선: ${diagonalDp.toStringAsFixed(1)}dp', tag: _tag);

    // 정확한 타블렛 감지 조건 (7인치 이상 AND 최소 너비 600dp 이상)
    // 7인치 = 672dp (96dpi 기준: 7 * 96 = 672)
    const double minDiagonalDp = 672.0; // 7인치
    const double minShortestSideDp = 600.0; // 최소 너비

    // 타블렛 조건: 대각선 7인치 이상 AND 최소 너비 600dp 이상 (AND 조건)
    if (diagonalDp >= minDiagonalDp && shortestSide >= minShortestSideDp) {
      LoggerUtils.logInfo('  ✅ 타블렛 감지: 대각선 ${diagonalDp.toStringAsFixed(1)}dp >= ${minDiagonalDp}dp AND 최소 변 ${shortestSide.toStringAsFixed(1)}dp >= ${minShortestSideDp}dp', tag: _tag);
      return true;
    }

    // 조건 불충족 시 상세 로그
    if (diagonalDp >= minDiagonalDp) {
      LoggerUtils.logInfo('  📱 스마트폰 감지: 대각선은 충족(${diagonalDp.toStringAsFixed(1)}dp >= ${minDiagonalDp}dp)하지만 최소 변 부족(${shortestSide.toStringAsFixed(1)}dp < ${minShortestSideDp}dp)', tag: _tag);
    } else if (shortestSide >= minShortestSideDp) {
      LoggerUtils.logInfo('  📱 스마트폰 감지: 최소 변은 충족(${shortestSide.toStringAsFixed(1)}dp >= ${minShortestSideDp}dp)하지만 대각선 부족(${diagonalDp.toStringAsFixed(1)}dp < ${minDiagonalDp}dp)', tag: _tag);
    } else {
      LoggerUtils.logInfo('  📱 스마트폰 감지: 대각선(${diagonalDp.toStringAsFixed(1)}dp < ${minDiagonalDp}dp)과 최소 변(${shortestSide.toStringAsFixed(1)}dp < ${minShortestSideDp}dp) 모두 부족', tag: _tag);
    }

    return false;
  }

  /// 탭 인덱스 변경 (외부에서 호출 가능)
  void setTabIndex(int index) {
    if (index >= 0 && index < 5) {
      setState(() {
        _currentTabIndex = index;
      });
    }
  }



  @override
  void dispose() {
    _instance = null; // 인스턴스 정리
    LoggerUtils.methodStart('dispose', tag: _tag);

    // 메모리 정리 강화
    try {
      MobilePerformanceUtils.stopMemoryMonitoring();
      MobilePerformanceUtils.performMemoryCleanup();
      _cleanupOptimizationSystems();

      // 추가 메모리 정리
      NetworkStatusUtil.dispose();
      CommonUtils.disposeDebounceTimer();

    } catch (e) {
      LoggerUtils.logError('AppWrapper dispose 중 오류', tag: _tag, error: e);
    }

    WidgetsBinding.instance.removeObserver(this);
    LoggerUtils.methodEnd('dispose', tag: _tag);
    super.dispose();
  }



  void _cleanupOptimizationSystems() {
    try {
      StateSyncManager().clearAllStates();
      LoggerUtils.logInfo('최적화 시스템 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('최적화 시스템 정리 실패', tag: _tag, error: e);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LoggerUtils.methodStart('didChangeAppLifecycleState', tag: _tag, data: {'state': state.name});
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _refreshProvidersIfNeeded();
        }
      });
    } else if (state == AppLifecycleState.detached || state == AppLifecycleState.paused) {
      // 앱 종료 시 메모리 누수 방지를 위한 정리
      try {
        NetworkStatusUtil.dispose();
        CommonUtils.disposeDebounceTimer();
        LoggerUtils.logInfo('앱 종료 시 메모리 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('앱 종료 시 메모리 정리 실패', tag: _tag, error: e);
      }
    }
    LoggerUtils.methodEnd('didChangeAppLifecycleState', tag: _tag);
  }

  Future<void> _refreshProvidersIfNeeded() async {
    if (!mounted) return;
    LoggerUtils.methodStart('_refreshProvidersIfNeeded', tag: _tag);
    try {
      // 실시간 동기화가 활성화되어 있으므로 수동 새로고침은 불필요
      LoggerUtils.logInfo('실시간 동기화 활성화로 인해 수동 새로고침 생략', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('Provider 상태 새로고침 실패', tag: _tag, error: e, stackTrace: stackTrace);
    }
    LoggerUtils.methodEnd('_refreshProvidersIfNeeded', tag: _tag);
  }

  Future<bool> _detectNewDevice() async {
    try {
      final workspaceState = ref.read(unifiedWorkspaceProvider);
      final hasWorkspaces = workspaceState.workspaces.isNotEmpty;
      if (!hasWorkspaces) return true;
      final dataSyncService = ref.read(dataSyncServiceProvider);
      final hasServerData = await dataSyncService.hasServerData();
      if (hasServerData && workspaceState.workspaces.length <= 1) return true;
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 새 디바이스를 위한 전체 다운로드 수행
  Future<void> _performFullDownloadForNewDevice() async {
    try {
      final dataSyncService = ref.read(dataSyncServiceProvider);

      LoggerUtils.logInfo('새 디바이스 전체 다운로드 시작', tag: _tag);

      await dataSyncService.performBidirectionalSync(
        onProgress: (message) {
          LoggerUtils.logInfo('새 디바이스 동기화: $message', tag: _tag);
        },
        onError: (error) {
          LoggerUtils.logError('새 디바이스 동기화 오류: $error', tag: _tag);
        },
      );

      LoggerUtils.logInfo('새 디바이스 전체 다운로드 완료', tag: _tag);

      // 워크스페이스 Provider 새로고침
      await ref.read(unifiedWorkspaceProvider.notifier).refresh();

    } catch (e) {
      LoggerUtils.logError('새 디바이스 전체 다운로드 실패', tag: _tag, error: e);
      // 실패해도 차분 동기화는 계속 진행
    }
  }

  void _onTabTapped(int index) {
    if (_currentTabIndex != index) {
      setState(() => _currentTabIndex = index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          final shouldExit = await ConfirmationDialog.show(
            context: context,
            title: '앱 종료',
            message: '앱을 종료하시겠습니까?',
            confirmLabel: '예',
            cancelLabel: '아니오',
          );
          if (shouldExit == true && context.mounted) {
            if (Theme.of(context).platform == TargetPlatform.android ||
                Theme.of(context).platform == TargetPlatform.iOS) {
              SystemNavigator.pop();
            } else {
              // 다른 플랫폼 (Web, Desktop 등)
              Navigator.of(context).popUntil((route) => route.isFirst);
            }
          }
        },
        child: Consumer(
          builder: (context, ref, _) {
            final nickname = ref.watch(nicknameProvider);
            final isLoggingOut = ref.watch(logoutStateProvider);

            // 로그아웃 중이면 로딩 화면 표시
            if (isLoggingOut) {
              LoggerUtils.logInfo('→ 로그아웃 진행 중 - 로딩 화면 표시', tag: 'AppWrapper');
              return const Scaffold(
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('로그아웃 처리 중...'),
                    ],
                  ),
                ),
              );
            }

            if (nickname == null) {
              // Firebase 사용자가 없으면 로그인 페이지로 이동해야 함
              final user = FirebaseAuth.instance.currentUser;
              LoggerUtils.logInfo('닉네임이 null - Firebase 사용자 확인: ${user?.email ?? 'null'}', tag: 'AppWrapper');

              if (user == null) {
                LoggerUtils.logInfo('→ 로그아웃 상태 감지 - 로그인 필요', tag: 'AppWrapper');
                // 로그아웃 상태에서는 NicknameScreen으로 가지 않고 로딩 표시
                return const Scaffold(
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('로그아웃 처리 중...'),
                      ],
                    ),
                  ),
                );
              }

              LoggerUtils.logInfo('→ NicknameScreen으로 이동 (사용자: ${user.email})', tag: 'AppWrapper');
              return NicknameScreen(
                onNicknameSet: () {
                  // setState 제거 - Riverpod이 자동으로 리빌드 처리
                  LoggerUtils.logInfo('닉네임 설정 완료 - Riverpod 자동 리빌드', tag: 'AppWrapper');
                },
              );
            }

            return _buildWorkspaceCheck();
          },
        ),
      ),
    );
  }

  /// 워크스페이스 확인 로직
  Widget _buildWorkspaceCheck() {
    // 닉네임이 있으면 행사 워크스페이스 존재 확인
    final workspaceState = ref.watch(unifiedWorkspaceProvider);

    if (!workspaceState.hasWorkspaces) {
      // 행사 워크스페이스가 없으면 행사 워크스페이스 생성 온보딩 화면으로 이동
      return EventWorkspaceOnboardingScreen(
        onWorkspaceCreated: () {
          LoggerUtils.logInfo('행사 워크스페이스 생성 완료 - Riverpod 자동 리빌드', tag: _tag);
          // setState 제거 - Riverpod이 자동으로 리빌드 처리
        },
      );
    }

    // 행사 워크스페이스는 있지만 현재 행사 워크스페이스가 설정되지 않은 경우
    if (!workspaceState.hasCurrentWorkspace) {
      // 로딩 화면 표시
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('행사 워크스페이스 정보를 불러오는 중...'),
            ],
          ),
        ),
      );
    }

    // 정상적으로 현재 행사 워크스페이스가 설정된 경우 하단 탭 네비게이션 표시
    return _buildMainScreenWithBottomTabs();
  }

  Widget _buildMainScreenWithBottomTabs() {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: _buildCurrentTabContent(),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: _buildFloatingPOSButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildCurrentTabContent() {
    return IndexedStack(
      index: _currentTabIndex,
      children: [
        _buildHomeTab(),
        widget.child,
        Container(),
        const RecordsAndStatisticsScreen(),
        _buildMyTab(),
      ],
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFE0E0E0), // 11번가 스타일 구분선
            width: 1.0,
          ),
        ),
      ),
      child: BottomAppBar(
        shape: const CircularNotchedRectangle(), // 중앙에 노치 생성
        notchMargin: 8.0, // 노치 여백
        color: Colors.white, // 깔끔한 하얀색 배경
        elevation: 0.0, // Container에서 구분선 처리하므로 elevation 제거
        child: SizedBox(
          height: 60,
          child: Row(
            children: [
              Expanded(child: _buildNavItem(0, LucideIcons.home, '홈')),
              Expanded(child: _buildNavItem(1, LucideIcons.creditCard, '선입금')),
              Expanded(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: _currentTabIndex == 2 ? RadialGradient(
                      center: Alignment.center,
                      radius: 1.2,
                      colors: [
                        AppColors.primarySeed.withValues(alpha: 0.15), // 중앙 진함
                        AppColors.primarySeed.withValues(alpha: 0.08), // 중간
                        AppColors.primarySeed.withValues(alpha: 0.03), // 가장자리 연함
                        Colors.transparent, // 완전 투명
                      ],
                      stops: const [0.0, 0.4, 0.7, 1.0],
                    ) : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        // POS 버튼과 동일하게 SaleScreen으로 이동
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SaleScreen(),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(12),
                      splashColor: AppColors.primarySeed.withValues(alpha: 0.25), // 더 진한 ripple
                      highlightColor: AppColors.primarySeed.withValues(alpha: 0.12), // 더 진한 highlight
                      child: SizedBox(
                        width: double.infinity,
                        height: double.infinity,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            const SizedBox(height: 32), // FloatingActionButton 공간 확보
                            Text(
                              'POS',
                              style: TextStyle(
                                color: const Color(0xFF9E9E9E), // 항상 비활성 색상으로 표시
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 4), // 하단 여백
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ), // 중앙 FAB 공간 + POS 텍스트
              Expanded(child: _buildNavItem(3, LucideIcons.fileBarChart, '기록&통계')),
              Expanded(child: _buildNavItem(4, LucideIcons.user, 'MY')),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    final isSelected = _currentTabIndex == index;
    
  return AnimatedContainer(
      duration: const Duration(milliseconds: 200), // 부드러운 애니메이션 전환
      curve: Curves.easeInOut,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        // 선택된 아이템에 그라데이션 효과 적용
        gradient: isSelected ? RadialGradient(
          center: Alignment.center,
          radius: 1.2, // 더 자연스러운 확산
          colors: [
            AppColors.primarySeed.withValues(alpha: 0.15), // 중앙 진함
            AppColors.primarySeed.withValues(alpha: 0.08), // 중간
            AppColors.primarySeed.withValues(alpha: 0.03), // 가장자리 연함
            Colors.transparent, // 완전 투명
          ],
          stops: const [0.0, 0.4, 0.7, 1.0], // 그라데이션 구간 세밀하게 조정
        ) : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onTabTapped(index),
          borderRadius: BorderRadius.circular(12),
          // 더 미묘한 ripple 색상
          splashColor: AppColors.primarySeed.withValues(alpha: 0.15),
          highlightColor: AppColors.primarySeed.withValues(alpha: 0.08),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 24,
                  color: isSelected 
                    ? AppColors.primarySeed // 선택된 아이템은 primarySeed 색상
                    : const Color(0xFF9E9E9E), // 비선택 아이템은 회색
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected 
                      ? AppColors.primarySeed // 선택된 아이템은 primarySeed 색상
                      : const Color(0xFF9E9E9E), // 비선택 아이템은 회색
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingPOSButton() {
    return Transform.translate(
      offset: const Offset(0, 8), // 버튼을 아래로 8px 이동
      child: FloatingActionButton(
        onPressed: () {
          // 새로운 페이지로 이동 (하단 앱바 숨김)
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const SaleScreen(),
            ),
          );
        },
        backgroundColor: AppColors.primarySeed, // 앱바와 같은 웜 테라코타
        foregroundColor: Colors.white, // 흰색 아이콘
        elevation: 8.0,
        child: const Icon(
          LucideIcons.shoppingCart,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildHomeTab() {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('리더보드', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: const HomeDashboardScreen(),
    );
  }

  Widget _buildMyTab() => const MyPageScreen();
}

/// 앱 종료 시 메모리 누수 방지를 위한 통합 정리 함수
///
/// 중위험 메모리 누수 항목들의 정적 리소스를 모두 정리합니다.
/// 앱의 생명주기 종료 시점에 호출되어야 합니다.
Future<void> cleanupAppResources() async {
  try {
    // Utils 클래스들의 정적 리소스 정리
    MobilePerformanceUtils.shutdown();
    LoggerUtils.shutdown();

    // Services 클래스들의 정적 리소스 정리
    RealtimeSyncService.shutdown();

    LoggerUtils.logInfo('앱 리소스 정리 완료');
  } catch (e) {
    // 정리 중 오류가 발생해도 앱 종료는 계속 진행
    LoggerUtils.logError('앱 리소스 정리 중 오류 발생', error: e);
  }
}

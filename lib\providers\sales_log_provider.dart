import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';

import '../models/sales_log.dart';
import '../models/sales_log_display_item.dart';
import '../models/sales_stat_item.dart';
import '../models/transaction_type.dart';
import '../models/event_workspace.dart';
import '../models/subscription_plan.dart';
import '../repositories/product_repository.dart';
import '../repositories/sales_log_repository.dart';
import '../utils/logger_utils.dart';
import '../utils/state_sync_manager.dart';
import '../utils/network_status.dart';
import '../services/database_service.dart';
import 'subscription_provider.dart';

import 'sales_log/sales_log_batch_operations.dart';
import 'sales_log/sales_log_filter_sort.dart';
import 'sales_log/sales_log_stats.dart';
import 'sales_log/sales_log_crud.dart';
import 'sales_log/sales_log_state.dart';
import 'unified_workspace_provider.dart';
import '../utils/event_workspace_utils.dart';
import 'realtime_sync_provider.dart';
import '../services/realtime_sync_service_main.dart';
import 'data_sync_provider.dart';
import 'product_provider.dart';

/// 판매 기록(영수증/로그) 상태를 관리하는 State 클래스입니다.
/// - 전체 판매 기록, 필터링/정렬 결과, 검색어, 로딩/에러/업데이트 상태 등 포함
/// - ProviderException, isCancelled 등 오류/취소/비동기 상태도 함께 관리

/// 상품 데이터베이스 접근을 위한 Repository Provider입니다.
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return ProductRepository(database: databaseService);
});

/// 판매 기록 데이터베이스 접근을 위한 Repository Provider입니다.
/// - DB 서비스와 연동하여 SalesLogRepository를 생성합니다.
final salesLogRepositoryProvider = Provider<SalesLogRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SalesLogRepository(database: databaseService);
});

/// 판매 기록 상태를 관리하는 Provider입니다.
class SalesLogNotifier extends StateNotifier<SalesLogState> {
  static const String _tag = 'SalesLogNotifier';

  final Ref ref;

  // 중복 다운로드 방지를 위한 플래그
  final Map<int, bool> _downloadingEvents = {};
  late SalesLogBatchOperations _batchOperations;
  late SalesLogFilterSort _filterSort;
  late SalesLogStats _stats;
  late SalesLogCrud _crud;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;

  // 무한 루프 방지를 위한 최근 추가한 판매 기록 캐시
  final Set<int> _recentlyAddedSalesLogs = <int>{};

  SalesLogNotifier(this.ref) : super(const SalesLogState()) {
    _initializeComponents();
    loadSalesLogs();
    _watchCurrentEvent();
    _setupRealtimeSync();
  }

  /// 현재 행사 워크스페이스 변경 감지 및 자동 새로고침
  void _watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo('현재 행사 워크스페이스 변경 감지 - SalesLogNotifier 새로고침: ${previous?.name} -> ${next?.name}', tag: _tag);
        
        // 이벤트 전환 시 메모리 정리
        _clearAllDataForEventTransition();
        
        if (next != null) {
          loadSalesLogs();
          _setupRealtimeSync(); // 새 워크스페이스에 대한 실시간 동기화 재설정
        } else {
          // 현재 행사 워크스페이스가 null이 되면 판매 기록 목록 클리어
          state = state.copyWith(
            salesLogs: [],
            errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
          );
        }
      }
    });
  }

  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();
      
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 실시간 동기화를 설정할 수 없습니다', tag: _tag);
        return;
      }
      
      // 실시간 동기화 서비스 가져오기
      final realtimeService = ref.read(realtimeSyncServiceProvider);
      
      // 중복 구독 방지: 이미 다른 곳에서 구독이 시작되었다고 가정
      LoggerUtils.logInfo('실시간 데이터 스트림 구독 시작 (중복 구독 방지)', tag: _tag);
      
      // 데이터 변경 리스너 설정
      _realtimeSubscription = realtimeService.dataChanges.listen((change) {
        _handleRealtimeDataChange(change);
      });
      
      LoggerUtils.logInfo('SalesLogNotifier 실시간 동기화 리스너 설정 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('SalesLogNotifier 실시간 동기화 설정 실패', tag: _tag, error: e);
    }
  }

  /// 실시간 데이터 변경 처리 - 개별 변경사항만 처리 (Local-First)
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      
      // 현재 워크스페이스의 판매 기록 변경인지 확인
      if (currentWorkspace?.id == change.eventId && change.collectionName == 'sales_logs') {
        LoggerUtils.logInfo('판매 기록 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: _tag);
        
        // 개별 변경사항만 처리 (전체 재로드 없이)
        _processSingleSalesLogChange(change);
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: _tag, error: e);
    }
  }

  /// 개별 판매 기록 변경사항 처리
  Future<void> _processSingleSalesLogChange(RealtimeDataChange change) async {
    if (!mounted) return;

    try {
      final salesLogId = int.tryParse(change.documentId);
      if (salesLogId == null) return;

      switch (change.changeType) {
        case RealtimeChangeType.added:
        case RealtimeChangeType.modified:
          // 자기가 최근에 추가한 판매 기록은 무시 (무한 루프 방지)
          if (_recentlyAddedSalesLogs.contains(salesLogId)) {
            LoggerUtils.logDebug('최근 추가한 판매 기록 무시: ID $salesLogId', tag: _tag);
            return;
          }

          if (change.data != null) {
            final salesLogData = Map<String, dynamic>.from(change.data!);
            salesLogData['id'] = salesLogId;
            final salesLog = SalesLog.fromJson(salesLogData);
            await _syncSalesLogToLocalSafe(salesLog);
            await _refreshSalesLogState();
          }
          break;
          
        case RealtimeChangeType.removed:
          // 안전한 삭제 처리 - 실제로 삭제되었는지 확인
          await _removeSalesLogFromLocalSafely(salesLogId, change.eventId);
          await _refreshSalesLogState();
          break;
      }
    } catch (e) {
      LoggerUtils.logError('개별 판매 기록 변경 처리 실패: ${change.documentId}', tag: _tag, error: e);
    }
  }

  /// 판매 기록을 로컬에 안전하게 동기화
  Future<void> _syncSalesLogToLocalSafe(SalesLog salesLog) async {
    try {
      final repository = ref.read(salesLogRepositoryProvider);
      
      // 기존 판매 기록 확인
      final existingSalesLog = await repository.getSalesLogById(salesLog.id);
      
      if (existingSalesLog != null) {
        // 업데이트
        await repository.updateSalesLog(salesLog);
        LoggerUtils.logDebug('판매 기록 로컬 업데이트 완료: ID ${salesLog.id}', tag: _tag);
      } else {
        // 새로 추가
        await repository.addSalesLog(salesLog);
        LoggerUtils.logDebug('판매 기록 로컬 추가 완료: ID ${salesLog.id}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('판매 기록 로컬 동기화 실패: ID ${salesLog.id}', tag: _tag, error: e);
    }
  }

  /// 판매 기록을 로컬에서 안전하게 제거 (안전성 강화 버전)
  Future<void> _removeSalesLogFromLocalSafely(int salesLogId, int eventId) async {
    try {
      // 1. 네트워크 연결 상태 확인
      if (!NetworkStatusUtil.isOnline) {
        LoggerUtils.logWarning('네트워크 연결 없음 - 판매 기록 삭제 보류: $salesLogId', tag: _tag);
        return;
      }

      // 2. 로컬에 해당 데이터가 실제로 존재하는지 확인
      final repository = ref.read(salesLogRepositoryProvider);
      final existingLog = await repository.getSalesLogById(salesLogId);

      if (existingLog == null) {
        LoggerUtils.logInfo('판매 기록이 로컬에 존재하지 않음 - 삭제 건너뛰기: $salesLogId', tag: _tag);
        return;
      }

      // 3. Firebase에서 실제로 삭제되었는지 재확인 (선택적)
      final user = FirebaseAuth.instance.currentUser;
      if (user != null && eventId > 0) {
        try {
          final doc = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('events')
              .doc(eventId.toString())
              .collection('sales_logs')
              .doc(salesLogId.toString())
              .get();

          if (doc.exists) {
            LoggerUtils.logWarning('Firebase에 여전히 존재하는 판매 기록 - 삭제하지 않음: $salesLogId', tag: _tag);
            return;
          }
        } catch (e) {
          LoggerUtils.logWarning('Firebase 재확인 실패 - 로컬 삭제 진행: $salesLogId', tag: _tag, error: e);
          // Firebase 확인 실패 시에도 로컬 삭제는 진행
        }
      }

      // 4. 로컬에서 삭제 수행
      await repository.deleteSalesLog(salesLogId);
      LoggerUtils.logInfo('판매 기록 안전 삭제 완료: $salesLogId', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('판매 기록 안전 삭제 실패: $salesLogId', tag: _tag, error: e);
      rethrow;
    }
  }



  /// 판매 기록 상태 새로고침 (로컬 DB에서 다시 로드) - 무한 루프 방지
  Future<void> _refreshSalesLogState() async {
    if (!mounted) return;

    try {
      // 이미 로딩 중이면 건너뛰기 (무한 루프 방지)
      if (state.isLoading || state.isUpdating) {
        LoggerUtils.logDebug('이미 로딩 중이므로 상태 새로고침 생략', tag: _tag);
        return;
      }

      await loadSalesLogs(showLoading: false);
    } catch (e) {
      LoggerUtils.logError('판매 기록 상태 새로고침 실패', tag: _tag, error: e);
    }
  }

  void _initializeComponents() {
    final repository = ref.read(salesLogRepositoryProvider);
    final productRepository = ref.read(productRepositoryProvider);
    _batchOperations = SalesLogBatchOperations(
      salesLogRepository: repository,
      updateState: (state) => updateState((current) => state),
    );
    _filterSort = SalesLogFilterSort(
      updateState: (state) => updateState((current) => state),
      createDisplayItems: _createDisplayItems,
    );
    _stats = SalesLogStats(
      salesLogRepository: repository,
      updateState: (state) => updateState((current) => state),
      ref: ref,
    );
    _crud = SalesLogCrud(
      salesLogRepository: repository,
      productRepository: productRepository,
      updateState: (state) => updateState((current) => state),
      refreshState: _refreshState,
      ref: ref,
    );
  }

  /// 페이지네이션을 사용한 판매 기록 로드 (POS 최적화)
  Future<void> loadSalesLogsPaginated({
    bool reset = false,
    String? searchQuery,
    String? sellerFilter,
  }) async {
    if (!mounted) return;

    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: _tag);
      if (!mounted) return;
      state = state.copyWith(
        salesLogs: [],
        isLoading: false,
        errorMessage: '행사 워크스페이스를 선택해주세요.',
      );
      return;
    }

    // 리셋이면 첫 페이지부터 시작
    final currentPage = reset ? 0 : state.currentPage;
    final isFirstLoad = reset || state.salesLogs.isEmpty;

    if (isFirstLoad) {
      state = state.copyWith(isLoading: true, currentPage: 0);
    } else {
      state = state.copyWith(isLoadingMore: true);
    }

    try {
      final repository = ref.read(salesLogRepositoryProvider);

      // 페이지네이션으로 판매 기록 조회
      final newSalesLogs = await repository.getSalesLogsByEventPaginated(
        eventId: currentWorkspace.id,
        page: currentPage,
        pageSize: state.pageSize,
        searchQuery: searchQuery,
        sellerFilter: sellerFilter,
      );

      // 총 개수 조회 (첫 로드시에만)
      int totalCount = state.totalCount;
      if (isFirstLoad) {
        totalCount = await repository.getSalesLogsCount(
          eventId: currentWorkspace.id,
          searchQuery: searchQuery,
          sellerFilter: sellerFilter,
        );
      }

      // 기존 데이터와 병합 또는 교체
      final updatedSalesLogs = isFirstLoad
          ? newSalesLogs
          : [...state.salesLogs, ...newSalesLogs];

      final hasMoreData = newSalesLogs.length == state.pageSize;

      if (!mounted) return;
      state = state.copyWith(
        salesLogs: updatedSalesLogs,
        currentPage: currentPage + 1,
        totalCount: totalCount,
        hasMoreData: hasMoreData,
        isLoading: false,
        isLoadingMore: false,
        errorMessage: null,
      );

      LoggerUtils.logInfo('페이지네이션 판매 기록 로드 완료: ${newSalesLogs.length}개 (총 ${updatedSalesLogs.length}개)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('페이지네이션 판매 기록 로드 실패', tag: _tag, error: e);
      if (!mounted) return;
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 클라이언트 사이드 페이지네이션 - 특정 페이지로 이동
  void goToPage(int page) {
    if (page < 1) return;

    final totalPages = (state.allSalesLogs.length / state.pageSize).ceil();
    if (page > totalPages && totalPages > 0) return;

    state = state.copyWith(currentPage: page);
    _updateDisplayedSalesLogs();
  }

  /// 다음 페이지로 이동
  void nextPage() {
    final totalPages = (state.allSalesLogs.length / state.pageSize).ceil();
    if (state.currentPage < totalPages) {
      goToPage(state.currentPage + 1);
    }
  }

  /// 이전 페이지로 이동
  void previousPage() {
    if (state.currentPage > 1) {
      goToPage(state.currentPage - 1);
    }
  }

  /// 페이지 크기 변경
  void changePageSize(int newPageSize) {
    if (newPageSize == state.pageSize) return;

    state = state.copyWith(
      pageSize: newPageSize,
      currentPage: 1, // 첫 페이지로 리셋
    );
    _updateDisplayedSalesLogs();
  }

  /// 현재 페이지에 표시할 데이터 업데이트
  void _updateDisplayedSalesLogs() {
    final startIndex = (state.currentPage - 1) * state.pageSize;
    final endIndex = (startIndex + state.pageSize).clamp(0, state.allSalesLogs.length);

    final displayedLogs = state.allSalesLogs.sublist(startIndex, endIndex);

    state = state.copyWith(
      salesLogs: displayedLogs,
      totalCount: state.allSalesLogs.length,
      hasMoreData: state.currentPage < (state.allSalesLogs.length / state.pageSize).ceil(),
    );
  }

  /// 기존 loadSalesLogs 메서드 (호환성 유지)
  Future<void> loadSalesLogs({bool showLoading = true}) async {
    if (!mounted) return;

    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }

    try {
      // 현재 선택된 행사 워크스페이스 확인
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: 'SalesLogNotifier');
        if (!mounted) return;
        state = state.copyWith(
          salesLogs: [],
          isLoading: false,
          errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
        );
        return;
      }

      // 로컬 DB에서 판매기록 로드 (모든 플랜에서 가능)
      final repository = ref.read(salesLogRepositoryProvider);
      final logs = await repository.getSalesLogsByEventId(currentWorkspace.id);
      if (!mounted) return;

      // 전체 데이터를 allSalesLogs에 저장하고, 첫 페이지 데이터만 salesLogs에 저장
      state = state.copyWith(
        allSalesLogs: logs,
        currentPage: 1,
        isLoading: false,
        errorMessage: null
      );

      // 첫 페이지 데이터 표시
      _updateDisplayedSalesLogs();

      // Firebase에서 최신 데이터 동기화 (프로 플랜에서만, 초기 로딩 시에만, 중복 방지)
      // 로컬 캐시에서 직접 플랜 확인 (서버 요청 방지)
      final subscriptionService = ref.read(subscriptionServiceProvider);
      final currentPlanType = await subscriptionService.getCachedSubscriptionPlan();
      final isProPlan = currentPlanType == SubscriptionPlanType.pro;

      // 프로 플랜이 아니면 Firebase 동기화만 건너뛰기 (로컬 데이터는 이미 로드됨)
      if (!isProPlan) {
        LoggerUtils.logInfo('프로 플랜이 아니므로 Firebase 동기화 건너뜀: $currentPlanType', tag: _tag);
        return;
      }

      // 실시간 동기화가 활성화된 경우 초기 동기화 건너뛰기
      final realtimeService = ref.read(realtimeSyncServiceProvider);
      final shouldSkipInitialSync = realtimeService.realtimeSyncEnabled;

      if (showLoading && !shouldSkipInitialSync && !_downloadingEvents.containsKey(currentWorkspace.id)) {
        _downloadingEvents[currentWorkspace.id] = true;
        try {
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.downloadSalesLogsFromFirebase(currentWorkspace.id);
          LoggerUtils.logInfo('판매기록 초기 동기화 완료', tag: _tag);
        } catch (e) {
          LoggerUtils.logWarning('판매기록 초기 동기화 실패 (로컬 데이터 사용)', tag: _tag, error: e);
          // Firebase 동기화 실패해도 계속 진행
        } finally {
          _downloadingEvents.remove(currentWorkspace.id);
        }
      } else if (shouldSkipInitialSync) {
        LoggerUtils.logInfo('실시간 동기화 활성화로 초기 동기화 건너뜀', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('판매기록 로딩 실패', tag: _tag, error: e);
      if (!mounted) return;
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      // 기존 salesLogs 데이터는 유지
    } finally {
      // 안전장치: 어떤 경우든 로딩 상태를 false로 설정
      if (mounted && state.isLoading) {
        state = state.copyWith(isLoading: false);
      }
    }
  }

  void updateState(SalesLogState Function(SalesLogState) update) {
    if (!mounted) return;
    state = update(state);
  }

  Future<void> _refreshState() async {
    if (!mounted) return;
    await loadSalesLogs();
  }

  List<SalesLogDisplayItem> _createDisplayItems(List<SalesLog> logs) {
    final Map<String?, List<SalesLog>> groupedLogs = {};
    final List<SalesLog> singleLogs = [];

    for (final log in logs) {
      final batchId = log.batchSaleId;
      if (batchId != null && batchId.isNotEmpty) {
        groupedLogs.putIfAbsent(batchId, () => []).add(log);
      } else {
        singleLogs.add(log);
      }
    }

    final List<SalesLogDisplayItem> displayItems = [];

    // 그룹화된 판매 추가
    for (final entry in groupedLogs.entries) {
      displayItems.add(GroupedSale(entry.value, entry.key!));
    }

    // 개별 판매 추가
    for (final log in singleLogs) {
      displayItems.add(SingleItem(log));
    }

    // 타임스탬프 기준 내림차순 정렬
    displayItems.sort(
      (a, b) => b.representativeTimestampMillis().compareTo(
        a.representativeTimestampMillis(),
      ),
    );

    return displayItems;
  }

  // ===== 배치 처리 메서드들 =====
  
  /// 배치 처리 취소
  void cancelBatchOperation() {
    _batchOperations.cancelBatchOperation();
  }

  /// 대용량 판매 기록 일괄 삽입
  Future<void> insertSalesLogsBatch(List<SalesLog> logs) async {
    await _batchOperations.insertSalesLogsBatch(logs);
  }

  /// 대용량 판매 기록 일괄 업데이트
  Future<void> updateSalesLogsBatch(List<SalesLog> logs) async {
    await _batchOperations.updateSalesLogsBatch(logs);
  }

  /// 대용량 판매 기록 일괄 삭제
  Future<void> deleteSalesLogsBatch(List<SalesLog> logs) async {
    await _batchOperations.deleteSalesLogsBatch(logs);
  }

  /// 판매 기록 일괄 처리
  Future<void> processBatchOperation({
    required List<String> logIds,
    required String operation,
  }) async {
    await _batchOperations.processBatchOperation(
      logIds: logIds,
      operation: operation,
    );
  }

  // ===== 검색/필터링/정렬 메서드들 =====

  /// 디바운스된 검색 기능
  void searchSalesLogs(String query) {
    final currentState = state;
    
    _filterSort.searchSalesLogs(query, currentState.salesLogs);
  }

  /// 디바운스된 필터링 기능
  void filterSalesLogs({
    String? sellerName,
    TransactionType? transactionType,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final currentState = state;
    
    _filterSort.filterSalesLogs(
      salesLogs: currentState.salesLogs,
      sellerName: sellerName,
      transactionType: transactionType,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// 디바운스된 정렬 기능
  void sortSalesLogs(String sortBy, bool ascending) {
    final currentState = state;
    
    _filterSort.sortSalesLogs(sortBy, ascending, currentState.salesLogs);
  }

  /// 필터 초기화
  void clearFilters() {
    final currentState = state;
    
    _filterSort.clearFilters(currentState.salesLogs);
  }

  /// 거래 타입 필터 설정
  void setTypeFilter(TransactionType? transactionType) {
    final currentState = state;
    
    _filterSort.setTypeFilter(transactionType, currentState.salesLogs);
  }

  /// 판매자 필터 설정
  void setSellerFilter(String sellerName) {
    final currentState = state;
    
    _filterSort.setSellerFilter(sellerName, currentState.salesLogs);
  }

  // ===== 통계 메서드들 =====

  /// 전체 판매 통계 로드
  Future<List<SalesStatItem>> loadOverallSalesStats() async {
    return await _stats.loadOverallSalesStats();
  }

  /// 총 판매 금액 계산
  Future<int> getTotalSalesAmount({
    int? startTime,
    int? endTime,
    String? sellerName,
  }) async {
    return await _stats.getTotalSalesAmount(
      startTime: startTime,
      endTime: endTime,
      sellerName: sellerName,
    );
  }

  /// 총 할인 금액 계산
  Future<int> getTotalDiscountAmount({
    int? startTime,
    int? endTime,
    String? sellerName,
  }) async {
    return await _stats.getTotalDiscountAmount(
      startTime: startTime,
      endTime: endTime,
      sellerName: sellerName,
    );
  }

  // ===== CRUD 메서드들 =====

  /// 판매 기록 추가
  Future<void> addSalesLog(SalesLog newSalesLog) async {
    if (!mounted) return;

    LoggerUtils.logDebug('판매 로그 추가 시작', tag: _tag);
    try {
      // 현재 선택된 행사 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      if (currentEvent == null) {
        LoggerUtils.logWarning('현재 선택된 행사가 없습니다', tag: _tag);
        if (!mounted) return;
        state = state.copyWith(errorMessage: '행사를 선택해주세요');
        return;
      }

      // 판매 기록에 현재 행사 ID 설정
      final salesLogWithEventId = newSalesLog.copyWith(eventId: currentEvent.id!);

      // DB에 저장하고 실제 ID를 가진 SalesLog 반환받기
      final savedSalesLog = await _crud.addSalesLog(salesLogWithEventId);
      LoggerUtils.logDebug('판매 로그 저장 완료: ID ${savedSalesLog.id}', tag: _tag);

      // 최근 추가한 판매 기록으로 캐시 (무한 루프 방지용)
      _recentlyAddedSalesLogs.add(savedSalesLog.id);
      // 5초 후 캐시에서 제거
      Future.delayed(const Duration(seconds: 5), () {
        _recentlyAddedSalesLogs.remove(savedSalesLog.id);
      });

      // 2. Firebase에 즉시 업로드 (실시간 동기화를 위해 - 플랜별 제한)
      try {
        // 플랜별 서버 동기화 기능 확인
        final subscriptionService = ref.read(subscriptionServiceProvider);
        final currentPlan = await subscriptionService.getCurrentPlan();

        if (currentPlan.hasServerSyncFeature) {
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.uploadSingleSalesLog(savedSalesLog);
          LoggerUtils.logInfo('판매 기록 Firebase 업로드 성공: ${savedSalesLog.productName}', tag: _tag);
        } else {
          LoggerUtils.logInfo('${currentPlan.name}은 서버 동기화 기능이 없어 Firebase 업로드를 건너뜁니다', tag: _tag);
        }
      } catch (e) {
        LoggerUtils.logError('판매 기록 Firebase 업로드 실패 (로컬 저장은 성공): ${savedSalesLog.productName}', tag: _tag, error: e);
        // Firebase 업로드 실패해도 로컬 저장은 성공했으므로 계속 진행
      }

      // 효율적인 상태 갱신: 실제 DB ID를 가진 로그를 현재 상태에 추가
      LoggerUtils.logDebug('판매 로그 상태 갱신 시작', tag: _tag);
      final currentLogs = List<SalesLog>.from(state.salesLogs);
      currentLogs.add(savedSalesLog);

      final newDisplayItems = _createDisplayItems(currentLogs);

      if (!mounted) return;
      state = state.copyWith(
        salesLogs: currentLogs,
        filteredSalesLogs: currentLogs,
        displayItems: newDisplayItems,
      );
      LoggerUtils.logDebug('판매 로그 상태 갱신 완료', tag: _tag);
      
    } catch (e) {
      LoggerUtils.logError('판매 로그 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록 수정
  Future<void> updateSalesLog(SalesLog salesLog) async {
    if (!mounted) return;

    try {
      await _crud.updateSalesLog(salesLog);

      // 실시간 동기화: 업데이트된 판매 기록을 Firebase에 즉시 업로드 (플랜별 제한)
      try {
        // 플랜별 서버 동기화 기능 확인
        final subscriptionService = ref.read(subscriptionServiceProvider);
        final currentPlan = await subscriptionService.getCurrentPlan();

        if (currentPlan.hasServerSyncFeature) {
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.uploadSingleSalesLog(salesLog);
          LoggerUtils.logInfo('판매 기록 업데이트 실시간 동기화 업로드 완료: ID ${salesLog.id}', tag: _tag);
        } else {
          LoggerUtils.logInfo('${currentPlan.name}은 서버 동기화 기능이 없어 Firebase 업데이트를 건너뜁니다', tag: _tag);
        }
      } catch (e) {
        LoggerUtils.logError('판매 기록 업데이트 실시간 동기화 업로드 실패: ID ${salesLog.id}', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 업데이트는 유지
      }

      // 관련 Provider들에게 판매 기록 업데이트 알림
      _notifyRelatedProviders(salesLog);
    } catch (e) {
      LoggerUtils.logError('판매 기록 수정 실패: ID ${salesLog.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록의 특정 필드만 업데이트 (선택적 업데이트)
  Future<void> updateSalesLogFields(int salesLogId, Map<String, dynamic> fields) async {
    if (!mounted) return;

    try {
      LoggerUtils.methodStart('updateSalesLogFields', tag: _tag, data: {'id': salesLogId, 'fields': fields.keys.toList()});

      // 현재 판매 기록 조회
      final currentSalesLog = state.salesLogs.firstWhere(
        (log) => log.id == salesLogId,
        orElse: () => throw Exception('판매 기록을 찾을 수 없습니다: $salesLogId'),
      );

      // 필드 업데이트를 위한 copyWith 호출
      SalesLog updatedSalesLog = currentSalesLog;

      // 각 필드별로 업데이트 적용
      if (fields.containsKey('productName')) {
        updatedSalesLog = updatedSalesLog.copyWith(productName: fields['productName'] as String);
      }

      if (fields.containsKey('sellerName')) {
        final sellerName = fields['sellerName'] as String?;
        updatedSalesLog = updatedSalesLog.copyWith(sellerName: sellerName);
      }

      if (fields.containsKey('soldPrice')) {
        updatedSalesLog = updatedSalesLog.copyWith(soldPrice: fields['soldPrice'] as int);
      }

      if (fields.containsKey('soldQuantity')) {
        updatedSalesLog = updatedSalesLog.copyWith(soldQuantity: fields['soldQuantity'] as int);
      }

      if (fields.containsKey('totalAmount')) {
        updatedSalesLog = updatedSalesLog.copyWith(totalAmount: fields['totalAmount'] as int);
      }

      if (fields.containsKey('transactionType')) {
        updatedSalesLog = updatedSalesLog.copyWith(transactionType: fields['transactionType'] as TransactionType);
      }

      if (fields.containsKey('setDiscountAmount')) {
        updatedSalesLog = updatedSalesLog.copyWith(setDiscountAmount: fields['setDiscountAmount'] as int);
      }

      if (fields.containsKey('setDiscountNames')) {
        final setDiscountNames = fields['setDiscountNames'] as String?;
        updatedSalesLog = updatedSalesLog.copyWith(setDiscountNames: setDiscountNames);
      }

      if (fields.containsKey('manualDiscountAmount')) {
        updatedSalesLog = updatedSalesLog.copyWith(manualDiscountAmount: fields['manualDiscountAmount'] as int);
      }

      if (fields.containsKey('paymentMethod')) {
        final paymentMethod = fields['paymentMethod'] as String?;
        updatedSalesLog = updatedSalesLog.copyWith(paymentMethod: paymentMethod);
      }

      // 로컬 DB 업데이트
      await _crud.updateSalesLog(updatedSalesLog);

      // 실시간 동기화: 변경된 필드만 Firebase에 업로드
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSalesLogFields(salesLogId, fields, eventId: updatedSalesLog.eventId);
        LoggerUtils.logInfo('판매 기록 필드 업데이트 실시간 동기화 업로드 완료: ${fields.keys.join(', ')}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('판매 기록 필드 업데이트 실시간 동기화 업로드 실패', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 업데이트는 유지
      }

      // 관련 Provider들에게 판매 기록 업데이트 알림
      _notifyRelatedProviders(updatedSalesLog);

      // 상태 갱신
      await loadSalesLogs(showLoading: false);

      LoggerUtils.methodEnd('updateSalesLogFields', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '판매 기록 필드 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': salesLogId, 'fields': fields.keys.toList()},
      );

      if (mounted) {
        state = state.copyWith(
          errorMessage: '판매 기록 필드 업데이트에 실패했습니다: ${e.toString()}',
        );
      }
      rethrow;
    }
  }

  /// 판매자 이름 변경 시 해당 판매자의 모든 판매 기록 업데이트
  Future<void> updateSellerNameForAllSalesLogs(String oldName, String newName, int eventId) async {
    try {
      LoggerUtils.logInfo('판매자 이름 변경으로 인한 판매 기록 일괄 업데이트 시작: $oldName → $newName', tag: _tag);

      // 해당 판매자의 모든 판매 기록 조회
      final allSalesLogs = state.salesLogs;
      final salesLogsToUpdate = allSalesLogs.where((log) =>
        log.sellerName == oldName && log.eventId == eventId
      ).toList();

      if (salesLogsToUpdate.isEmpty) {
        LoggerUtils.logInfo('업데이트할 판매 기록이 없습니다: $oldName', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('업데이트할 판매 기록 수: ${salesLogsToUpdate.length}개', tag: _tag);

      // 각 판매 기록의 판매자 이름 업데이트
      for (final salesLog in salesLogsToUpdate) {
        final updatedSalesLog = salesLog.copyWith(sellerName: newName);
        await _crud.updateSalesLog(updatedSalesLog);
      }

      // 상태 갱신
      if (mounted) {
        await loadSalesLogs();
      }

      LoggerUtils.logInfo('판매자 이름 변경으로 인한 판매 기록 일괄 업데이트 완료: ${salesLogsToUpdate.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 이름 변경으로 인한 판매 기록 일괄 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 관련 Provider들에게 판매 기록 업데이트 알림
  void _notifyRelatedProviders(SalesLog updatedSalesLog) {
    try {
      // 백그라운드에서 관련 Provider들 갱신 (UI 블로킹 방지)
      Future.microtask(() async {
        try {
          // 상품 Provider 갱신 (재고 변경이 있는 경우)
          ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);

          LoggerUtils.logInfo('판매 기록 관련 Provider들 갱신 완료: ${updatedSalesLog.productName}', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('판매 기록 관련 Provider 갱신 실패', tag: _tag, error: e);
        }
      });
    } catch (e) {
      LoggerUtils.logError('판매 기록 관련 Provider 알림 실패', tag: _tag, error: e);
    }
  }

  /// 배치 Sales Log 추가 (판매 처리 최적화용)
  Future<List<SalesLog>> batchAddSalesLogs(List<SalesLog> salesLogs) async {
    LoggerUtils.logInfo('배치 Sales Log 추가 시작: ${salesLogs.length}개', tag: _tag);

    try {
      final savedSalesLogs = <SalesLog>[];

      // 현재 행사 확인
      final currentEvent = ref.read(currentWorkspaceProvider);
      if (currentEvent == null) {
        throw Exception('현재 행사가 선택되지 않았습니다.');
      }

      // 1. 로컬 DB 배치 저장 (중복 방지 강화)
      for (final salesLog in salesLogs) {
        final salesLogWithEventId = salesLog.copyWith(eventId: currentEvent.id);

        // 중복 체크: 같은 batchSaleId, productId, soldQuantity, saleTimestamp를 가진 기록이 있는지 확인
        final isDuplicate = await _checkDuplicateSalesLog(salesLogWithEventId);
        if (isDuplicate) {
          LoggerUtils.logWarning('중복 판매 기록 감지, 건너뜀: ${salesLogWithEventId.productName}', tag: _tag);
          continue;
        }

        final savedSalesLog = await _crud.addSalesLog(salesLogWithEventId);
        savedSalesLogs.add(savedSalesLog);

        // 최근 추가한 판매 기록으로 캐시 (무한 루프 방지용)
        _recentlyAddedSalesLogs.add(savedSalesLog.id);
        // 10초 후 캐시에서 제거 (5초에서 10초로 증가)
        Future.delayed(const Duration(seconds: 10), () {
          _recentlyAddedSalesLogs.remove(savedSalesLog.id);
        });
      }

      // 2. 상태 갱신 (중복 방지)
      final currentState = state;
      final existingIds = currentState.salesLogs.map((log) => log.id).toSet();
      final newLogs = savedSalesLogs.where((log) => !existingIds.contains(log.id)).toList();
      final updatedLogs = [...newLogs, ...currentState.salesLogs];
      state = currentState.copyWith(salesLogs: updatedLogs);

      // 3. 백그라운드에서 Firebase 배치 동기화
      _syncSalesLogsBatchInBackground(savedSalesLogs);

      LoggerUtils.logInfo('배치 Sales Log 추가 완료: ${savedSalesLogs.length}개', tag: _tag);
      return savedSalesLogs;
    } catch (e) {
      LoggerUtils.logError('배치 Sales Log 추가 실패', tag: _tag, error: e);
      state = state.copyWith(
        errorMessage: e.toString(),
        errorCode: 'SALES_LOG_BATCH_ADD_ERROR',
        errorSeverity: 'high',
      );
      return [];
    }
  }

  /// 중복 판매 기록 체크
  Future<bool> _checkDuplicateSalesLog(SalesLog salesLog) async {
    try {
      // 현재 상태의 판매 기록들 중에서 중복 체크
      final currentLogs = state.salesLogs;

      // 같은 batchSaleId, productId, soldQuantity를 가진 기록이 최근 1분 내에 있는지 확인
      final recentTimestamp = DateTime.now().millisecondsSinceEpoch - (60 * 1000); // 1분 전

      final duplicates = currentLogs.where((log) =>
        log.batchSaleId == salesLog.batchSaleId &&
        log.productId == salesLog.productId &&
        log.soldQuantity == salesLog.soldQuantity &&
        log.saleTimestamp >= recentTimestamp
      ).toList();

      return duplicates.isNotEmpty;
    } catch (e) {
      LoggerUtils.logError('중복 판매 기록 체크 실패', tag: _tag, error: e);
      return false; // 에러 시 중복이 아닌 것으로 처리
    }
  }

  /// 배치 Sales Log Firebase 동기화 (백그라운드 - 플랜별 제한)
  void _syncSalesLogsBatchInBackground(List<SalesLog> salesLogs) {
    if (salesLogs.isEmpty) return;

    Future.microtask(() async {
      try {
        // 플랜별 서버 동기화 기능 확인
        final subscriptionService = ref.read(subscriptionServiceProvider);
        final currentPlan = await subscriptionService.getCurrentPlan();

        if (currentPlan.hasServerSyncFeature) {
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.uploadSalesLogsBatch(salesLogs);
          LoggerUtils.logInfo('배치 Sales Log Firebase 동기화 완료: ${salesLogs.length}개', tag: _tag);
        } else {
          LoggerUtils.logInfo('${currentPlan.name}은 서버 동기화 기능이 없어 Firebase 동기화를 건너뜁니다', tag: _tag);
        }
      } catch (e) {
        LoggerUtils.logError('배치 Sales Log Firebase 동기화 실패: ${salesLogs.length}개', tag: _tag, error: e);
      }
    });
  }

  /// 완전한 판매 기록 삭제 (재고 복구 + Firebase 동기화 포함)
  ///
  /// 모든 삭제 요구사항을 충족하는 통합 메서드입니다.
  /// 이 메서드를 사용하면 재고 복구와 Firebase 동기화가 모두 처리됩니다.
  Future<String> deleteSalesLogComplete(SalesLog salesLog) async {
    if (!mounted) return '삭제 실패: 화면이 종료되었습니다.';

    try {
      return await _crud.deleteSalesLogComplete(salesLog);
    } catch (e) {
      LoggerUtils.logError('완전한 판매 기록 삭제 실패: ID ${salesLog.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 묶음 판매 기록 삭제 및 재고 복구
  Future<String> deleteGroupedSaleAndUpdateStock(GroupedSale groupedSale) async {
    return await _crud.deleteGroupedSaleAndUpdateStock(groupedSale);
  }



  /// 판매 기록 ID로 조회
  Future<SalesLog?> getSalesLogById(int id) async {
    return await _crud.getSalesLogById(id);
  }

  /// 판매자별 판매 기록 로드
  Future<void> loadSalesLogsBySeller(String sellerName) async {
    await _crud.loadSalesLogsBySeller(sellerName);
  }

  /// 거래 타입별 판매 기록 로드
  Future<void> loadSalesLogsByType(TransactionType transactionType) async {
    await _crud.loadSalesLogsByType(transactionType);
  }

  /// 판매자 및 거래 타입별 판매 기록 로드
  Future<void> loadSalesLogsBySellerAndType(
    String sellerName,
    TransactionType transactionType,
  ) async {
    await _crud.loadSalesLogsBySellerAndType(sellerName, transactionType);
  }

  /// 날짜 범위별 판매 기록 로드
  Future<void> loadSalesLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    await _crud.loadSalesLogsByDateRange(startDate, endDate);
  }

  /// 배치 ID별 판매 기록 로드
  Future<void> loadSalesLogsByBatchId(String batchId) async {
    await _crud.loadSalesLogsByBatchId(batchId);
  }

  /// 작업 취소 메서드
  void cancelOperation(String operationName) {
    // StateNotifier에서는 단순히 상태를 업데이트하여 취소 처리
    state = state.copyWith(isCancelled: true);
  }

  /// 진행 중인 작업 취소
  void cancelCurrentOperation() {
    cancelOperation('loadSalesLogs');
    cancelOperation('processBatchOperation');
  }

  /// 거래 타입 이름 가져오기
  String getTransactionTypeName(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return '판매';
      case TransactionType.service:
        return '서비스';
      case TransactionType.discount:
        return '할인';

      case TransactionType.setDiscount:
        return '세트 할인';
    }
  }

  /// 이벤트 전환 시 모든 데이터 정리 - 메모리 누수 방지
  void _clearAllDataForEventTransition() {
    try {
      if (kDebugMode) {
        LoggerUtils.logInfo('이벤트 전환 - SalesLogNotifier 데이터 정리 시작', tag: _tag);
      }

      // 실시간 구독 해제
      _realtimeSubscription?.cancel();
      _realtimeSubscription = null;

      // 상태 완전 초기화
      state = const SalesLogState();

      if (kDebugMode) {
        LoggerUtils.logInfo('이벤트 전환 - SalesLogNotifier 데이터 정리 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('SalesLogNotifier 데이터 정리 중 오류: $e', tag: _tag);
    }
  }

  @override
  void dispose() {
    // 실시간 구독 정리
    _realtimeSubscription?.cancel();
    _realtimeSubscription = null;
    super.dispose();
  }
}

final salesLogNotifierProvider = StateNotifierProvider<SalesLogNotifier, SalesLogState>((ref) {
  final notifier = SalesLogNotifier(ref);

  // StateSyncManager에 상태 캐시 등록 (상태 변경 시 자동 업데이트)
  notifier.addListener((state) {
    StateSyncManager().cacheState('sales_log', state);
  });

  // 초기 상태 캐시
  StateSyncManager().cacheState('sales_log', notifier.state);

  // Provider dispose 시 캐시 정리
  ref.onDispose(() {
    try {
      StateSyncManager().clearAllStates(); // 전체 캐시 정리
    } catch (e) {
      // 정리 실패 시 무시 (앱 종료 시점일 수 있음)
    }
  });

  return notifier;
});

// 하단의 @riverpod 함수형 Provider들도 일반 Provider로 변환 필요
final salesLogsProvider = Provider<List<SalesLog>>((ref) {
  return ref.watch(salesLogNotifierProvider).salesLogs;
});
final filteredSalesLogsProvider = Provider<List<SalesLog>>((ref) {
  return ref.watch(salesLogNotifierProvider).filteredSalesLogs;
});
final salesLogDisplayItemsProvider = Provider<List<SalesLogDisplayItem>>((ref) {
  return ref.watch(salesLogNotifierProvider).displayItems;
});
final salesLogSellerNamesProvider = Provider<List<String>>((ref) {
  return ref.watch(salesLogNotifierProvider).sellerNames;
});
final salesStatsProvider = Provider<List<SalesStatItem>>((ref) {
  return ref.watch(salesLogNotifierProvider).salesStats;
});
final salesLogIsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(salesLogNotifierProvider).isLoading;
});
final salesLogErrorMessageProvider = Provider<String?>((ref) {
  return ref.watch(salesLogNotifierProvider).errorMessage;
});
final salesLogErrorCodeProvider = Provider<String?>((ref) {
  return ref.watch(salesLogNotifierProvider).errorCode;
});

/// Sales Log PaginationController Provider
// PaginationController, PaginationState, pagedSalesLogControllerProvider 등 페이징 관련 타입/Provider/변수/주석 전체 제거
// 전체 불러오기 구조로 통일
// import 누락/불필요 import 정리

/// Sales Log PaginationState Provider
// PaginationState, pagedSalesLogStateProvider 등 페이징 관련 Provider/타입/변수/주석/분기 완전 삭제
// 전체 불러오기 구조로 통일
// import 누락/불필요 import 정리

/// Sales Log 데이터 동기화 관리자
class SalesLogDataSyncManager {
  static const String _tag = 'SalesLogDataSyncManager';
  
  /// 모든 Sales Log 관련 Provider를 일관되게 갱신
  static Future<void> syncAllSalesLogData(WidgetRef ref) async {
    LoggerUtils.logInfo('Sales Log 데이터 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Sales Log Notifier 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      
      LoggerUtils.logInfo('Sales Log 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Sales Log 추가 후 동기화
  static Future<void> syncAfterAddSalesLog(WidgetRef ref, SalesLog newSalesLog) async {
    LoggerUtils.logInfo('Sales Log 추가 후 동기화 시작', tag: _tag);

    try {
      // 단순화된 Sales Log Notifier 추가
      await ref.read(salesLogNotifierProvider.notifier).addSalesLog(newSalesLog);

      LoggerUtils.logInfo('Sales Log 추가 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 추가 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }




  
  /// Sales Log 수정 후 동기화
  static Future<void> syncAfterUpdateSalesLog(WidgetRef ref, SalesLog salesLog) async {
    LoggerUtils.logInfo('Sales Log 수정 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Sales Log Notifier 수정
      await ref.read(salesLogNotifierProvider.notifier).updateSalesLog(salesLog);
      
      LoggerUtils.logInfo('Sales Log 수정 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 수정 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Sales Log 삭제 후 동기화 (완전한 삭제 사용)
  static Future<void> syncAfterDeleteSalesLog(WidgetRef ref, SalesLog salesLog) async {
    LoggerUtils.logInfo('Sales Log 삭제 후 동기화 시작', tag: _tag);

    try {
      // 완전한 Sales Log 삭제 (재고 복구 + Firebase 동기화 포함)
      await ref.read(salesLogNotifierProvider.notifier).deleteSalesLogComplete(salesLog);

      LoggerUtils.logInfo('Sales Log 삭제 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Sales Log 삭제 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}

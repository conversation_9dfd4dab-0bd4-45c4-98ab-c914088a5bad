/// 바라 부스 매니저 - 나이스페이 실제 운영 구독 서비스
///
/// 나이스페이 v1 API를 사용한 실제 운영 환경 구독 서비스입니다.
/// - 실제 운영 키 사용
/// - 신 버전 API 전용 (v1)
/// - Firebase Auth 연동
/// - 자동 재결제 시스템
/// - 구독 상태 관리
///
/// 작성자: Blue
/// 버전: 2.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:http/http.dart' as http;


import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../utils/logger_utils.dart';
import '../models/subscription_plan.dart';
import '../services/remote_config_service.dart';
import 'subscription_service.dart';

/// 나이스페이 구독 서비스 (실제 운영 환경)
class NicePaySubscriptionService {
  static const String _tag = 'NicePaySubscriptionService';
  
  // 🔥 실제 운영 환경 설정
  static const String _baseUrl = 'https://api.nicepay.co.kr';
  static const String _clientKey = 'R2_88ae8fc2a6474a44ad2d3dbb8f331a46';
  static const String _secretKey = '0a6e0a5737e14f25be3e529f609ca40b';
  
  // Authorization 헤더용 Base64 인코딩된 credentials
  static final String _credentials = base64Encode(utf8.encode('$_clientKey:$_secretKey'));
  
  // Firebase 인스턴스
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // 구독 플랜 설정
  static const int _plusPrice = 0; // 플러스 플랜 가격 (무료로 변경)
  static const int _proPrice = 4900; // 프로 플랜 가격 (4900원으로 변경)
  static const String _plusGoodsName = '바라 부스 매니저 플러스 플랜 (무료)';
  static const String _proGoodsName = '바라 부스 매니저 프로 플랜';
  
  /// 싱글톤 인스턴스
  static final NicePaySubscriptionService _instance = NicePaySubscriptionService._internal();
  factory NicePaySubscriptionService() => _instance;
  NicePaySubscriptionService._internal();

  /// 현재 로그인된 사용자 ID 가져오기
  String? get _currentUserId => _auth.currentUser?.uid;

  /// 빌링키 발급 (카드 등록)
  Future<SubscriptionResult> registerCard({
    required String cardNo,
    required String expYear,
    required String expMonth,
    required String idNo,
    required String cardPw,
    required String buyerName,
    required String buyerEmail,
    required String buyerTel,
  }) async {
    try {
      if (_currentUserId == null) {
        return SubscriptionResult.error('로그인이 필요합니다.');
      }

      LoggerUtils.logInfo('빌링키 발급 시작', tag: _tag);

      // orderId 생성 (고유한 주문번호)
      final orderId = 'CARD_${DateTime.now().millisecondsSinceEpoch}_${_currentUserId!.substring(0, 8)}';

      // 카드 정보 암호화 (AES-128)
      final cardData = 'cardNo=$cardNo&expYear=$expYear&expMonth=$expMonth&idNo=$idNo&cardPw=$cardPw';
      final encData = _encryptAES128(cardData);

      // ediDate 생성 (YYYYMMDDHHMMSS 형식)
      final now = DateTime.now();
      final ediDate = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';

      // signData 생성: hex(sha256(orderId + ediDate + SecretKey))
      final signDataInput = '$orderId$ediDate$_secretKey';
      final signData = _generateSha256Hash(signDataInput);

      // 요청 데이터 구성
      final requestData = {
        'encData': encData,
        'orderId': orderId,
        'buyerName': buyerName,
        'buyerEmail': buyerEmail,
        'buyerTel': buyerTel,
        'ediDate': ediDate,
        'signData': signData,
        'returnCharSet': 'utf-8',
      };

      LoggerUtils.logInfo('빌링키 발급 요청: $requestData', tag: _tag);

      // API 호출 (타임아웃 및 에러 처리 개선)
      final response = await http.post(
        Uri.parse('$_baseUrl/v1/subscribe/regist'),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': 'Basic $_credentials',
        },
        body: json.encode(requestData),
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException('카드 등록 요청 시간이 초과되었습니다.', const Duration(seconds: 30));
        },
      );

      LoggerUtils.logInfo('빌링키 발급 응답: ${response.statusCode}', tag: _tag);
      LoggerUtils.logInfo('빌링키 발급 응답 본문: ${response.body}', tag: _tag);

      final responseData = json.decode(response.body);

      if (responseData['resultCode'] == '0000') {
        final bid = responseData['bid'];
        LoggerUtils.logInfo('빌링키 발급 성공: $bid', tag: _tag);

        // Firestore에 카드 정보 저장
        await _saveCardInfo(bid, responseData, buyerName, buyerEmail, buyerTel, cardNo);

        return SubscriptionResult.success(data: {
          'bid': bid,
          'orderId': orderId,
          'cardInfo': responseData,
        });
      } else {
        LoggerUtils.logError('빌링키 발급 실패: ${responseData['resultMsg']}', tag: _tag);
        return SubscriptionResult.error(responseData['resultMsg']);
      }
    } on TimeoutException catch (e) {
      LoggerUtils.logError('빌링키 발급 타임아웃: $e', tag: _tag);
      return SubscriptionResult.error('카드 등록 요청 시간이 초과되었습니다. 네트워크 연결을 확인해주세요.');
    } on SocketException catch (e) {
      LoggerUtils.logError('빌링키 발급 네트워크 오류: $e', tag: _tag);
      return SubscriptionResult.error('네트워크 연결에 문제가 있습니다. 인터넷 연결을 확인해주세요.');
    } catch (e) {
      LoggerUtils.logError('빌링키 발급 오류: $e', tag: _tag);
      return SubscriptionResult.error('카드 등록 중 오류가 발생했습니다: $e');
    }
  }

  /// 플러스 플랜 구독 시작 (이벤트 기간 중 무료)
  Future<SubscriptionResult> startPlusSubscription(String bid) async {
    try {
      if (_currentUserId == null) {
        return SubscriptionResult.error('로그인이 필요합니다.');
      }

      LoggerUtils.logInfo('플러스 플랜 구독 시작: $bid', tag: _tag);

      // 앱 버전 체크 (Remote Config)
      final eligibilityResult = await RemoteConfigService.checkSubscriptionEligibility();
      if (!eligibilityResult.isEligible) {
        LoggerUtils.logWarning('앱 버전이 낮아 플러스 플랜 구독 불가: ${eligibilityResult.currentVersion} < ${eligibilityResult.requiredVersion}', tag: _tag);
        return SubscriptionResult.error(eligibilityResult.message ?? '앱을 최신 버전으로 업데이트해주세요.');
      }

      // 플러스 플랜 무료 기간 체크
      final isFreeNow = await RemoteConfigService.isPlusPlanFreeNow();

      if (isFreeNow) {
        // 무료 기간 중이면 결제 없이 바로 구독 정보 저장
        final fakePaymentData = {
          'orderId': 'free_plus_${DateTime.now().millisecondsSinceEpoch}',
          'amount': 0,
          'goodsName': _plusGoodsName,
          'paymentMethod': 'FREE_EVENT',
          'resultCode': '0000',
          'resultMsg': '오픈 이벤트 무료 플랜 활성화',
        };

        // 구독 정보 저장
        await _saveSubscriptionInfo(bid, fakePaymentData, SubscriptionPlanType.plus);

        // 기존 SubscriptionService도 플러스 플랜으로 업데이트
        final subscriptionService = SubscriptionService();
        await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.plus);

        LoggerUtils.logInfo('플러스 플랜 구독 시작 완료 (무료 이벤트)', tag: _tag);
        return SubscriptionResult.success(data: fakePaymentData);
      } else {
        // 무료 기간이 아니면 정상 결제 진행
        final paymentResult = await _processPayment(bid, '플러스 플랜 구독 결제', SubscriptionPlanType.plus);

        if (paymentResult.isSuccess) {
          // 구독 정보 저장
          await _saveSubscriptionInfo(bid, paymentResult.data!, SubscriptionPlanType.plus);

          // 기존 SubscriptionService도 플러스 플랜으로 업데이트
          final subscriptionService = SubscriptionService();
          await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.plus);

          LoggerUtils.logInfo('플러스 플랜 구독 시작 완료 (유료)', tag: _tag);
          return SubscriptionResult.success(data: paymentResult.data);
        } else {
          return paymentResult;
        }
      }
    } catch (e) {
      LoggerUtils.logError('플러스 플랜 구독 시작 오류: $e', tag: _tag);
      return SubscriptionResult.error('플러스 플랜 구독 시작 중 오류가 발생했습니다: $e');
    }
  }

  /// 프로 플랜 구독 시작
  Future<SubscriptionResult> startProSubscription(String bid) async {
    try {
      if (_currentUserId == null) {
        return SubscriptionResult.error('로그인이 필요합니다.');
      }

      LoggerUtils.logInfo('프로 플랜 구독 시작: $bid', tag: _tag);

      // 앱 버전 체크 (Remote Config)
      final eligibilityResult = await RemoteConfigService.checkSubscriptionEligibility();
      if (!eligibilityResult.isEligible) {
        LoggerUtils.logWarning('앱 버전이 낮아 프로 플랜 구독 불가: ${eligibilityResult.currentVersion} < ${eligibilityResult.requiredVersion}', tag: _tag);
        return SubscriptionResult.error(eligibilityResult.message ?? '앱을 최신 버전으로 업데이트해주세요.');
      }

      // 프로 플랜 결제 실행
      final paymentResult = await _processPayment(bid, '프로 플랜 구독 결제', SubscriptionPlanType.pro);

      if (paymentResult.isSuccess) {
        // 구독 정보 저장
        await _saveSubscriptionInfo(bid, paymentResult.data!, SubscriptionPlanType.pro);

        // 기존 SubscriptionService도 프로 플랜으로 업데이트
        final subscriptionService = SubscriptionService();
        await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.pro);

        LoggerUtils.logInfo('프로 플랜 구독 시작 완료', tag: _tag);
        return SubscriptionResult.success(data: paymentResult.data);
      } else {
        return paymentResult;
      }
    } catch (e) {
      LoggerUtils.logError('프로 플랜 구독 시작 오류: $e', tag: _tag);
      return SubscriptionResult.error('프로 플랜 구독 시작 중 오류가 발생했습니다: $e');
    }
  }

  /// 구독 시작 (플랜 타입에 따라 처리)
  Future<SubscriptionResult> startSubscription(String bid, SubscriptionPlanType planType) async {
    LoggerUtils.logInfo('구독 시작 요청: BID=$bid, 플랜=$planType', tag: _tag);

    switch (planType) {
      case SubscriptionPlanType.plus:
        LoggerUtils.logInfo('플러스 플랜 구독 시작', tag: _tag);
        return await startPlusSubscription(bid);
      case SubscriptionPlanType.pro:
        LoggerUtils.logInfo('프로 플랜 구독 시작', tag: _tag);
        return await startProSubscription(bid);
      case SubscriptionPlanType.free:
        LoggerUtils.logError('무료 플랜 구독 시도', tag: _tag);
        return SubscriptionResult.error('무료 플랜은 구독할 수 없습니다.');
    }
  }

  /// 구독 취소 (다음 결제일까지 유지)
  Future<SubscriptionResult> cancelSubscription() async {
    try {
      if (_currentUserId == null) {
        return SubscriptionResult.error('로그인이 필요합니다.');
      }

      LoggerUtils.logInfo('구독 취소 시작', tag: _tag);

      // 현재 구독 정보 가져오기
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        return SubscriptionResult.error('활성화된 구독이 없습니다.');
      }

      // 🔥 구독 상태를 "취소 예정"으로 변경 (다음 결제일까지 유지)
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('subscriptions')
          .doc('current')
          .update({
        'status': 'cancel_scheduled', // 즉시 취소가 아닌 예정 취소
        'cancelledAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'willCancelAt': subscription['nextPaymentDate'], // 다음 결제일에 취소
      });

      // 🔥 SubscriptionService도 동기화 (다음 결제일까지는 PRO 플랜 유지)
      // 실제 취소는 다음 결제일에 Cloud Functions에서 처리

      // 🔥 기존 SubscriptionService는 다음 결제일까지 PRO 유지
      // (실제 취소는 정기 결제 시스템에서 처리)

      LoggerUtils.logInfo('구독 취소 예약 완료 - 다음 결제일까지 PRO 플랜 유지', tag: _tag);
      return SubscriptionResult.success();
    } catch (e) {
      LoggerUtils.logError('구독 취소 오류: $e', tag: _tag);
      return SubscriptionResult.error('구독 취소 중 오류가 발생했습니다: $e');
    }
  }

  /// 프로레이션 결제 처리 (플러스 → 프로 업그레이드)
  Future<SubscriptionResult> processProrationPayment(
    String bid,
    int amount,
    String description,
  ) async {
    try {
      if (_currentUserId == null) {
        return SubscriptionResult.error('로그인이 필요합니다.');
      }

      LoggerUtils.logInfo('프로레이션 결제 시작: $amount원', tag: _tag);

      // 🔥 일반 결제와 동일한 플로우 사용 - 커스텀 금액과 상품명으로 결제 처리
      final paymentResult = await _processPaymentWithCustomAmount(
        bid,
        description,
        customAmount: amount,
        customGoodsName: description,
      );

      if (paymentResult.isSuccess) {
        // 🔥 일반 결제와 동일한 플로우 사용 - 구독 정보 저장
        await _saveSubscriptionInfo(bid, paymentResult.data!, SubscriptionPlanType.pro);

        // 🔥 일반 결제와 동일한 플로우 사용 - SubscriptionService 업데이트
        final subscriptionService = SubscriptionService();
        await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.pro);

        LoggerUtils.logInfo('프로레이션 결제 완료 - 일반 결제와 동일한 플로우 사용', tag: _tag);
        return SubscriptionResult.success(data: paymentResult.data);
      } else {
        return paymentResult;
      }
    } catch (e) {
      LoggerUtils.logError('프로레이션 결제 오류: $e', tag: _tag);
      return SubscriptionResult.error('프로레이션 결제 중 오류가 발생했습니다: $e');
    }
  }

  /// 결제 취소
  Future<SubscriptionResult> cancelPayment({
    required String tid,
    required String orderId,
    required String reason,
    int? cancelAmt,
  }) async {
    try {
      LoggerUtils.logInfo('결제 취소 시작: TID=$tid', tag: _tag);

      // ediDate 생성 (YYYYMMDDHHMMSS 형식)
      final now = DateTime.now();
      final ediDate = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';

      // signData 생성: hex(sha256(tid + ediDate + SecretKey))
      final signDataInput = '$tid$ediDate$_secretKey';
      final signData = _generateSha256Hash(signDataInput);

      // 요청 데이터 구성
      final requestData = <String, dynamic>{
        'reason': reason,
        'orderId': orderId,
        'ediDate': ediDate,
        'signData': signData,
        'returnCharSet': 'utf-8',
      };

      // 부분취소인 경우 금액 추가
      if (cancelAmt != null) {
        requestData['cancelAmt'] = cancelAmt;
      }

      LoggerUtils.logInfo('결제 취소 요청: $requestData', tag: _tag);

      // API 호출
      final response = await http.post(
        Uri.parse('$_baseUrl/v1/payments/$tid/cancel'),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': 'Basic $_credentials',
        },
        body: json.encode(requestData),
      );

      LoggerUtils.logInfo('결제 취소 응답: ${response.statusCode}', tag: _tag);
      LoggerUtils.logInfo('결제 취소 응답 본문: ${response.body}', tag: _tag);

      final responseData = json.decode(response.body);

      if (responseData['resultCode'] == '0000') {
        LoggerUtils.logInfo('결제 취소 성공', tag: _tag);
        return SubscriptionResult.success(data: responseData);
      } else {
        LoggerUtils.logError('결제 취소 실패: ${responseData['resultMsg']}', tag: _tag);
        return SubscriptionResult.error(responseData['resultMsg']);
      }
    } catch (e) {
      LoggerUtils.logError('결제 취소 오류: $e', tag: _tag);
      return SubscriptionResult.error('결제 취소 중 오류가 발생했습니다: $e');
    }
  }

  /// 현재 구독 정보 가져오기
  Future<Map<String, dynamic>?> getCurrentSubscription() async {
    try {
      if (_currentUserId == null) return null;

      final doc = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('subscriptions')
          .doc('current')
          .get();

      return doc.exists ? doc.data() : null;
    } catch (e) {
      LoggerUtils.logError('구독 정보 조회 오류: $e', tag: _tag);
      return null;
    }
  }

  /// 결제 내역 가져오기
  Future<List<Map<String, dynamic>>> getPaymentHistory() async {
    try {
      if (_currentUserId == null) return [];

      final querySnapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('payments')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) => doc.data()).toList();
    } catch (e) {
      LoggerUtils.logError('결제 내역 조회 오류: $e', tag: _tag);
      return [];
    }
  }

  /// 등록된 카드 정보 가져오기
  Future<Map<String, dynamic>?> getRegisteredCard() async {
    try {
      if (_currentUserId == null) return null;

      final querySnapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('cards')
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty ? querySnapshot.docs.first.data() : null;
    } catch (e) {
      LoggerUtils.logError('카드 정보 조회 오류: $e', tag: _tag);
      return null;
    }
  }

  /// 결제 처리 (내부 메서드) - 플랜 타입 기반
  Future<SubscriptionResult> _processPayment(String bid, String description, [SubscriptionPlanType planType = SubscriptionPlanType.pro]) async {
    return _processPaymentWithCustomAmount(bid, description, planType: planType);
  }

  /// 결제 처리 (내부 메서드) - 커스텀 금액 및 상품명 지원
  Future<SubscriptionResult> _processPaymentWithCustomAmount(
    String bid,
    String description, {
    SubscriptionPlanType? planType,
    int? customAmount,
    String? customGoodsName,
  }) async {
    try {
      // 등록된 카드 정보에서 구매자 정보 가져오기
      final cardInfo = await getRegisteredCard();

      // orderId 생성
      final orderId = 'PAY_${DateTime.now().millisecondsSinceEpoch}_${_currentUserId!.substring(0, 8)}';

      // ediDate 생성 (YYYYMMDDHHMMSS 형식)
      final now = DateTime.now();
      final ediDate = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';

      // signData 생성: hex(sha256(orderId + bid + ediDate + SecretKey))
      final signDataInput = '$orderId$bid$ediDate$_secretKey';
      final signData = _generateSha256Hash(signDataInput);

      // 🔥 실제 구매자 정보 사용
      final buyerName = cardInfo?['buyerName'] ?? _auth.currentUser?.displayName ?? '구매자';
      final buyerEmail = cardInfo?['buyerEmail'] ?? _auth.currentUser?.email ?? '<EMAIL>';
      final buyerTel = cardInfo?['buyerTel'] ?? '01012345678';

      // 금액과 상품명 설정 (커스텀 값 우선, 없으면 플랜별 기본값)
      int amount;
      String goodsName;

      if (customAmount != null && customGoodsName != null) {
        // 커스텀 금액과 상품명 사용 (프로레이션 결제용)
        amount = customAmount;
        goodsName = customGoodsName;
      } else if (planType != null) {
        // 플랜별 기본 금액과 상품명 사용
        switch (planType) {
          case SubscriptionPlanType.plus:
            amount = _plusPrice;
            goodsName = _plusGoodsName;
            break;
          case SubscriptionPlanType.pro:
            amount = _proPrice;
            goodsName = _proGoodsName;
            break;
          case SubscriptionPlanType.free:
            throw Exception('무료 플랜은 결제할 수 없습니다.');
        }
      } else {
        throw Exception('플랜 타입 또는 커스텀 금액/상품명이 필요합니다.');
      }

      // 요청 데이터 구성
      final requestData = {
        'orderId': orderId,
        'amount': amount,
        'goodsName': goodsName,
        'cardQuota': '0', // 일시불
        'useShopInterest': false,
        'buyerName': buyerName,
        'buyerEmail': buyerEmail,
        'buyerTel': buyerTel,
        'ediDate': ediDate,
        'signData': signData,
        'returnCharSet': 'utf-8',
      };

      LoggerUtils.logInfo('결제 요청: $requestData', tag: _tag);

      // API 호출
      final response = await http.post(
        Uri.parse('$_baseUrl/v1/subscribe/$bid/payments'),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': 'Basic $_credentials',
        },
        body: json.encode(requestData),
      );

      LoggerUtils.logInfo('결제 응답: ${response.statusCode}', tag: _tag);
      LoggerUtils.logInfo('결제 응답 본문: ${response.body}', tag: _tag);

      final responseData = json.decode(response.body);

      if (responseData['resultCode'] == '0000') {
        LoggerUtils.logInfo('결제 성공: ${responseData['tid']}', tag: _tag);

        // 결제 내역 저장
        await _savePaymentHistory(responseData, description);

        return SubscriptionResult.success(data: responseData);
      } else {
        LoggerUtils.logError('결제 실패: ${responseData['resultMsg']}', tag: _tag);
        return SubscriptionResult.error(responseData['resultMsg']);
      }
    } catch (e) {
      LoggerUtils.logError('결제 처리 오류: $e', tag: _tag);
      return SubscriptionResult.error('결제 처리 중 오류가 발생했습니다: $e');
    }
  }

  /// AES-128 암호화
  String _encryptAES128(String plainText) {
    try {
      // SecretKey의 앞 16자리를 AES 키로 사용
      final aesKey = _secretKey.substring(0, 16);
      final keyBytes = utf8.encode(aesKey);
      final key128 = Key(Uint8List.fromList(keyBytes));

      // AES-128-ECB 모드로 암호화
      final encrypter = Encrypter(AES(key128, mode: AESMode.ecb));
      final encrypted = encrypter.encrypt(plainText);

      // Hex 인코딩으로 반환 (소문자)
      return encrypted.base16.toLowerCase();
    } catch (e) {
      LoggerUtils.logError('AES 암호화 오류: $e', tag: _tag);
      throw Exception('암호화 중 오류가 발생했습니다: $e');
    }
  }

  /// SHA-256 해시 생성
  String _generateSha256Hash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }



  /// 카드 정보 저장
  Future<void> _saveCardInfo(String bid, Map<String, dynamic> cardInfo, String buyerName, String buyerEmail, String buyerTel, [String? cardNo]) async {
    // 기존 활성 카드들을 비활성화
    final existingCards = await _firestore
        .collection('users')
        .doc(_currentUserId!)
        .collection('cards')
        .where('isActive', isEqualTo: true)
        .get();

    // 배치로 기존 카드들 비활성화
    final batch = _firestore.batch();
    for (final doc in existingCards.docs) {
      batch.update(doc.reference, {'isActive': false});
    }

    // 새 카드 정보 저장
    final newCardRef = _firestore
        .collection('users')
        .doc(_currentUserId!)
        .collection('cards')
        .doc(bid);

    batch.set(newCardRef, {
      'bid': bid,
      'buyerName': buyerName,
      'buyerEmail': buyerEmail,
      'buyerTel': buyerTel,
      'cardInfo': cardInfo,
      'maskedCardNo': cardNo != null && cardNo.length >= 4 ? '**** **** **** ${cardNo.substring(cardNo.length - 4)}' : null,
      'createdAt': FieldValue.serverTimestamp(),
      'isActive': true,
    });

    // 배치 실행
    await batch.commit();
  }

  /// 구독 정보 저장
  Future<void> _saveSubscriptionInfo(String bid, Map<String, dynamic> paymentData, [SubscriptionPlanType planType = SubscriptionPlanType.pro]) async {
    final now = DateTime.now();

    // 🔥 개별 구독일 기준으로 다음 결제일 계산
    final nextPaymentDate = _calculateNextPaymentDate(now);

    // 플랜별 정보 설정
    String planName;
    int price;
    switch (planType) {
      case SubscriptionPlanType.plus:
        planName = 'plus';
        price = _plusPrice;
        break;
      case SubscriptionPlanType.pro:
        planName = 'pro';
        price = _proPrice;
        break;
      case SubscriptionPlanType.free:
        throw Exception('무료 플랜은 저장할 수 없습니다.');
    }

    await _firestore
        .collection('users')
        .doc(_currentUserId!)
        .collection('subscriptions')
        .doc('current')
        .set({
      // UserSubscription 모델과 호환되는 필드명 사용
      'userId': _currentUserId!,
      'planType': planType.toString(),
      'subscriptionStartDate': now.toIso8601String(),
      'subscriptionEndDate': nextPaymentDate.toIso8601String(),
      'isActive': true,
      'createdAt': now.toIso8601String(),
      'updatedAt': now.toIso8601String(),

      // 나이스페이 관련 추가 정보
      'bid': bid,
      'status': 'active',
      'plan': planName,
      'price': price,
      'subscriptionDay': now.day,
      'nextPaymentDate': Timestamp.fromDate(nextPaymentDate),
      'lastPaymentData': paymentData,
    });
  }

  /// 다음 결제일 계산 (29, 30, 31일 구독자 처리 포함)
  DateTime _calculateNextPaymentDate(DateTime currentDate) {
    final nextMonth = DateTime(currentDate.year, currentDate.month + 1, currentDate.day);

    // 다음 달의 마지막 날 확인
    final lastDayOfNextMonth = DateTime(nextMonth.year, nextMonth.month + 1, 0).day;

    // 29, 30, 31일 구독자 처리
    if (currentDate.day > lastDayOfNextMonth) {
      return DateTime(nextMonth.year, nextMonth.month, lastDayOfNextMonth);
    } else {
      return nextMonth;
    }
  }

  /// 결제 내역 저장
  Future<void> _savePaymentHistory(Map<String, dynamic> paymentData, String description) async {
    await _firestore
        .collection('users')
        .doc(_currentUserId!)
        .collection('payments')
        .doc(paymentData['tid'])
        .set({
      'tid': paymentData['tid'],
      'orderId': paymentData['orderId'],
      'amount': paymentData['amount'],
      'status': paymentData['status'],
      'description': description,
      'paymentData': paymentData,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }
}

/// 구독 결과 클래스
class SubscriptionResult {
  final bool isSuccess;
  final String? error;
  final Map<String, dynamic>? data;

  SubscriptionResult._({
    required this.isSuccess,
    this.error,
    this.data,
  });

  factory SubscriptionResult.success({Map<String, dynamic>? data}) {
    return SubscriptionResult._(isSuccess: true, data: data);
  }

  factory SubscriptionResult.error(String error) {
    return SubscriptionResult._(isSuccess: false, error: error);
  }
}
